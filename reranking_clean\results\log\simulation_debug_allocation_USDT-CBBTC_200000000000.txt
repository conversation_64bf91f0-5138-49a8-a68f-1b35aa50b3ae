Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 8813
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 88.13%
  Available: 200000000000, Allocated: 176260000000
  Output: 159697589
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9998
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 8813}
next_token_balance: {'******************************************': 159697589}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.98%
  Available: 159697589, Allocated: 181170599
  Output: 181207586
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1185
flow_bps: {'******************************************': 10000, '******************************************': 8813}
next_flow_bps: {'******************************************': 0, '******************************************': 9998}
next_token_balance: {'******************************************': 0, '******************************************': 181207586}
--------------------------------
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 8813
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 88.13%
  Available: 200000000000, Allocated: 176260000000
  Output: 156000128
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9998
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 8813}
next_token_balance: {'******************************************': 156000128}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.98%
  Available: 156000128, Allocated: 176975976
  Output: 177108021
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1185
flow_bps: {'******************************************': 10000, '******************************************': 8813}
next_flow_bps: {'******************************************': 0, '******************************************': 9998}
next_token_balance: {'******************************************': 0, '******************************************': 177108021}
--------------------------------
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 8813
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 88.13%
  Available: 200000000000, Allocated: 176260000000
  Output: 156000128
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9998
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 8813}
next_token_balance: {'******************************************': 156000128}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.98%
  Available: 156000128, Allocated: 176975976
  Output: 177108021
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1185
flow_bps: {'******************************************': 10000, '******************************************': 8813}
next_flow_bps: {'******************************************': 0, '******************************************': 9998}
next_token_balance: {'******************************************': 0, '******************************************': 177108021}
--------------------------------

Fill 3/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 11.85%
  Available: 200000000000, Allocated: 23700000000
  Output: 20917950

Final output for ******************************************: 177108021
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 8813
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 88.13%
  Available: 200000000000, Allocated: 176260000000
  Output: 159697589
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9998
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 8813}
next_token_balance: {'******************************************': 159697589}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.98%
  Available: 159697589, Allocated: 181170599
  Output: 181207586
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1185
flow_bps: {'******************************************': 10000, '******************************************': 8813}
next_flow_bps: {'******************************************': 0, '******************************************': 9998}
next_token_balance: {'******************************************': 0, '******************************************': 181207586}
--------------------------------

Fill 3/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 11.85%
  Available: 200000000000, Allocated: 23700000000
  Output: 21425578

Final output for ******************************************: 181207586
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 7
Starting sequential simulation with 7 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 23.01%
  Available: 200000000000, Allocated: 46020000000
  Output: 46021129910
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 2301}
next_token_balance: {'******************************************': 46021129910}
--------------------------------

Fill 2/7: ****************************************** -> ******************************************
  Pool: 0x9a772018fbd77fcd2d25657e5c547baff3fd7d16
  Source: Uniswap_V3
  Proportion: 23.01%
  Available: 46021129910, Allocated: 46021129910
  Output: 41386892
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9996
flow_bps: {'******************************************': 10000, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 2301}
next_token_balance: {'******************************************': 0, '******************************************': 41386892}
--------------------------------

Fill 3/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.96%
  Available: 41386892, Allocated: 179792860
  Output: 179800661
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 6158
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 179800661}
--------------------------------

Fill 4/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 61.58%
  Available: 200000000000, Allocated: 123160000000
  Output: 28675124253011098837
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 3897
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996, '******************************************': 0, '******************************************': 6158}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 179800661, '******************************************': 0, '******************************************': 28675124253011098837}
--------------------------------

Fill 5/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 38.97%
  Available: 28675124253011098837, Allocated: 18146631895742816256
  Output: 70048454
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2261
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 3897, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 70048454, '******************************************': 179800661, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 6/7: ****************************************** -> ******************************************
  Pool: 0xcbcdf9626bc03e24f779434178a73a0b4bad62ed
  Source: Uniswap_V3
  Proportion: 22.61%
  Available: 28675124253011098837, Allocated: 10528492357268283392
  Output: 40677439
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1537
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 6158, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 110725893, '******************************************': 179800661, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 7/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 15.37%
  Available: 200000000000, Allocated: 30740000000
  Output: 27585704

Final output for ******************************************: 179800661
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 7
Starting sequential simulation with 7 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 23.01%
  Available: 200000000000, Allocated: 46020000000
  Output: 46021129910
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 2301}
next_token_balance: {'******************************************': 46021129910}
--------------------------------

Fill 2/7: ****************************************** -> ******************************************
  Pool: 0x9a772018fbd77fcd2d25657e5c547baff3fd7d16
  Source: Uniswap_V3
  Proportion: 23.01%
  Available: 46021129910, Allocated: 46021129910
  Output: 41386892
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9996
flow_bps: {'******************************************': 10000, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 2301}
next_token_balance: {'******************************************': 0, '******************************************': 41386892}
--------------------------------

Fill 3/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.96%
  Available: 41386892, Allocated: 179792860
  Output: 179800661
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 6158
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 179800661}
--------------------------------

Fill 4/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 61.58%
  Available: 200000000000, Allocated: 123160000000
  Output: 28675124253011098837
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 3897
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996, '******************************************': 0, '******************************************': 6158}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 179800661, '******************************************': 0, '******************************************': 28675124253011098837}
--------------------------------

Fill 5/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 38.97%
  Available: 28675124253011098837, Allocated: 18146631895742816256
  Output: 70048454
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2261
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 3897, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 70048454, '******************************************': 179800661, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 6/7: ****************************************** -> ******************************************
  Pool: 0xcbcdf9626bc03e24f779434178a73a0b4bad62ed
  Source: Uniswap_V3
  Proportion: 22.61%
  Available: 28675124253011098837, Allocated: 10528492357268283392
  Output: 40677439
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1537
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 6158, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 110725893, '******************************************': 179800661, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 7/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 15.37%
  Available: 200000000000, Allocated: 30740000000
  Output: 27585704

Final output for ******************************************: 179800661
Starting API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 7
Starting topological simulation with 7 edges
Source node ****************************************** has initial amount 200000000000
Processing node ****************************************** with input 200000000000
Node ******************************************, input=200000000000, distributing among 3 edges (total BPS: 9996)
  Edge: ****************************************** -> ****************************************** via ******************************************
  Pool type: Uniswap_V3, Proportion: 23.01%
  Input: 46038415366
  Output: 46039545710
Node ****************************************** is now ready for processing (all 1 incoming edges processed)
  Edge: ****************************************** -> ****************************************** via ******************************************
  Pool type: Uniswap_V3, Proportion: 61.58%
  Input: 123209283713
  Output: 28686592021148098897
Node ****************************************** is now ready for processing (all 1 incoming edges processed)
  Edge: ****************************************** -> ****************************************** via ******************************************
  Pool type: Uniswap_V3, Proportion: 15.37%
  Input: 30752300920
  Output: 27596742
Processing node ****************************************** with input 46039545710
Node ******************************************, input=46039545710, distributing among 1 edges (total BPS: 2301)
  Edge: ****************************************** -> ****************************************** via 0x9a772018fbd77fcd2d25657e5c547baff3fd7d16
  Pool type: Uniswap_V3, Proportion: 23.01%
  Input: 46039545710
  Output: 41403415
Processing node ****************************************** with input 28686592021148098897
Node ******************************************, input=28686592021148098897, distributing among 2 edges (total BPS: 6158)
  Edge: ****************************************** -> ****************************************** via ******************************************
  Pool type: Uniswap_V3, Proportion: 38.97%
  Input: 18153889104646662144
  Output: 70076414
  Edge: ****************************************** -> ****************************************** via 0xcbcdf9626bc03e24f779434178a73a0b4bad62ed
  Pool type: Uniswap_V3, Proportion: 22.61%
  Input: 10532702916501436416
  Output: 40693706
Node ****************************************** is now ready for processing (all 4 incoming edges processed)
Processing node ****************************************** with input 179770277
Node ******************************************, input=179770277, distributing among 1 edges (total BPS: 9996)
  Edge: ****************************************** -> ****************************************** via ******************************************
  Pool type: Uniswap_V3, Proportion: 99.96%
  Input: 179770277
  Output: 179778077
Node ****************************************** is now ready for processing (all 1 incoming edges processed)
Processing node ****************************************** with input 179778077
Node ****************************************** has no outgoing edges
Final output for ******************************************: 179778077
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 7
Starting sequential simulation with 7 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 23.01%
  Available: 200000000000, Allocated: 46020000000
    V3 API simulation error: V3 API simulation error: Request execution error: HTTPConnectionPool(host='127.0.0.1', port=8080): Max retries exceeded with url: /get_amount_out_with_update (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000024D58F66960>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
  Output: 0
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 2301}
next_token_balance: {'******************************************': 0}
--------------------------------

Fill 2/7: ****************************************** -> ******************************************
  Pool: 0x9a772018fbd77fcd2d25657e5c547baff3fd7d16
  Source: Uniswap_V3
  Proportion: 23.01%
  Warning: No balance for input token ******************************************, skipping
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9996
flow_bps: {'******************************************': 10000, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 2301}
next_token_balance: {'******************************************': 0}
--------------------------------

Fill 3/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.96%
  Warning: No balance for input token ******************************************, skipping
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 6158
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996}
next_token_balance: {'******************************************': 0, '******************************************': 0}
--------------------------------

Fill 4/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 61.58%
  Available: 200000000000, Allocated: 123160000000
    V3 API simulation error: V3 API simulation error: Request execution error: HTTPConnectionPool(host='127.0.0.1', port=8080): Max retries exceeded with url: /get_amount_out_with_update (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000024D58F66D20>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
  Output: 0
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 3897
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996, '******************************************': 0, '******************************************': 6158}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 5/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 38.97%
  Warning: No balance for input token ******************************************, skipping
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2261
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 3897, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 6/7: ****************************************** -> ******************************************
  Pool: 0xcbcdf9626bc03e24f779434178a73a0b4bad62ed
  Source: Uniswap_V3
  Proportion: 22.61%
  Warning: No balance for input token ******************************************, skipping
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1537
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 6158, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 7/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 15.37%
  Available: 200000000000, Allocated: 30740000000
    V3 API simulation error: V3 API simulation error: Request execution error: HTTPConnectionPool(host='127.0.0.1', port=8080): Max retries exceeded with url: /get_amount_out_with_update (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000024D58F67E00>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
  Output: 0

Final output for ******************************************: 0
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 7
Starting sequential simulation with 7 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 23.01%
  Available: 200000000000, Allocated: 46020000000
  Output: 46021129910
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 2301}
next_token_balance: {'******************************************': 46021129910}
--------------------------------

Fill 2/7: ****************************************** -> ******************************************
  Pool: 0x9a772018fbd77fcd2d25657e5c547baff3fd7d16
  Source: Uniswap_V3
  Proportion: 23.01%
  Available: 46021129910, Allocated: 46021129910
  Output: 41386892
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9996
flow_bps: {'******************************************': 10000, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 2301}
next_token_balance: {'******************************************': 0, '******************************************': 41386892}
--------------------------------

Fill 3/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.96%
  Available: 41386892, Allocated: 179792860
  Output: 179800661
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 6158
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 179800661}
--------------------------------

Fill 4/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 61.58%
  Available: 200000000000, Allocated: 123160000000
  Output: 28675124253011098837
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 3897
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996, '******************************************': 0, '******************************************': 6158}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 179800661, '******************************************': 0, '******************************************': 28675124253011098837}
--------------------------------

Fill 5/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 38.97%
  Available: 28675124253011098837, Allocated: 18146631895742816256
  Output: 70048454
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2261
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 3897, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 70048454, '******************************************': 179800661, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 6/7: ****************************************** -> ******************************************
  Pool: 0xcbcdf9626bc03e24f779434178a73a0b4bad62ed
  Source: Uniswap_V3
  Proportion: 22.61%
  Available: 28675124253011098837, Allocated: 10528492357268283392
  Output: 40677439
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1537
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 6158, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 110725893, '******************************************': 179800661, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 7/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 15.37%
  Available: 200000000000, Allocated: 30740000000
  Output: 27585704

Final output for ******************************************: 179800661
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 8813
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 88.13%
  Available: 200000000000, Allocated: 176260000000
  Output: 159697589
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9998
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 8813}
next_token_balance: {'******************************************': 159697589}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.98%
  Available: 159697589, Allocated: 181170599
  Output: 181207586
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1185
flow_bps: {'******************************************': 10000, '******************************************': 8813}
next_flow_bps: {'******************************************': 0, '******************************************': 9998}
next_token_balance: {'******************************************': 0, '******************************************': 181207586}
--------------------------------

Fill 3/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 11.85%
  Available: 200000000000, Allocated: 23700000000
  Output: 21425578

Final output for ******************************************: 181207586
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 8813
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 88.13%
  Available: 200000000000, Allocated: 176260000000
  Output: 159697589
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9998
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 8813}
next_token_balance: {'******************************************': 159697589}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.98%
  Available: 159697589, Allocated: 181170599
  Output: 181207586
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1185
flow_bps: {'******************************************': 10000, '******************************************': 8813}
next_flow_bps: {'******************************************': 0, '******************************************': 9998}
next_token_balance: {'******************************************': 0, '******************************************': 181207586}
--------------------------------

Fill 3/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 11.85%
  Available: 200000000000, Allocated: 23700000000
  Output: 21425578

Final output for ******************************************: 181207586
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 7
Starting sequential simulation with 7 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {}
next_token_balance: {}
--------------------------------

Fill 1/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 23.01%
  Available: 200000000000, Allocated: 46020000000
  Output: 46021129910
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2301
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 2301}
next_token_balance: {'******************************************': 46021129910}
--------------------------------

Fill 2/7: ****************************************** -> ******************************************
  Pool: 0x9a772018fbd77fcd2d25657e5c547baff3fd7d16
  Source: Uniswap_V3
  Proportion: 23.01%
  Available: 46021129910, Allocated: 46021129910
  Output: 41386892
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9996
flow_bps: {'******************************************': 10000, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 2301}
next_token_balance: {'******************************************': 0, '******************************************': 41386892}
--------------------------------

Fill 3/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.96%
  Available: 41386892, Allocated: 179792860
  Output: 179800661
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 6158
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 179800661}
--------------------------------

Fill 4/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 61.58%
  Available: 200000000000, Allocated: 123160000000
  Output: 28675124253011098837
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 3897
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9996, '******************************************': 0, '******************************************': 6158}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 179800661, '******************************************': 0, '******************************************': 28675124253011098837}
--------------------------------

Fill 5/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 38.97%
  Available: 28675124253011098837, Allocated: 18146631895742816256
  Output: 70048454
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2261
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 3897, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 70048454, '******************************************': 179800661, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 6/7: ****************************************** -> ******************************************
  Pool: 0xcbcdf9626bc03e24f779434178a73a0b4bad62ed
  Source: Uniswap_V3
  Proportion: 22.61%
  Available: 28675124253011098837, Allocated: 10528492357268283392
  Output: 40677439
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1537
flow_bps: {'******************************************': 10000, '******************************************': 2301, '******************************************': 2301, '******************************************': 6158}
next_flow_bps: {'******************************************': 0, '******************************************': 6158, '******************************************': 9996, '******************************************': 0, '******************************************': 0}
next_token_balance: {'******************************************': 0, '******************************************': 110725893, '******************************************': 179800661, '******************************************': 0, '******************************************': 0}
--------------------------------

Fill 7/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 15.37%
  Available: 200000000000, Allocated: 30740000000
  Output: 27585704

Final output for ******************************************: 179800661
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 6
Starting sequential simulation with 6 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 4968
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0}
next_token_balance: {}
--------------------------------

Fill 1/6: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 49.68%
  Available: 200000000000, Allocated: 99360000000
  Output: 88016443
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 9998
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 4968}
next_token_balance: {'******************************************': 88016443}
--------------------------------

Fill 2/6: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 99.98%
  Available: 88016443, Allocated: 177131319
  Output: 177263476
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2208
flow_bps: {'******************************************': 10000, '******************************************': 4968}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9998}
next_token_balance: {'******************************************': 0, '******************************************': 177263476}
--------------------------------

Fill 3/6: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 22.08%
  Available: 200000000000, Allocated: 44160000000
  Output: 44154541503
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2208
flow_bps: {'******************************************': 10000, '******************************************': 4968}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 9998, '******************************************': 2208}
next_token_balance: {'******************************************': 0, '******************************************': 177263476, '******************************************': 44154541503}
--------------------------------

Fill 4/6: ****************************************** -> ******************************************
  Pool: 0x9a772018fbd77fcd2d25657e5c547baff3fd7d16
  Source: Uniswap_V3
  Proportion: 22.08%
  Available: 44154541503, Allocated: 44154541503
  Output: 39114873
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2822
flow_bps: {'******************************************': 10000, '******************************************': 4968, '******************************************': 2208}
next_flow_bps: {'******************************************': 0, '******************************************': 2208, '******************************************': 9998, '******************************************': 0}
next_token_balance: {'******************************************': 39114873, '******************************************': 177263476, '******************************************': 0}
--------------------------------

Fill 5/6: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 28.22%
  Available: 200000000000, Allocated: 56440000000
  Output: 11980262879406199430
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2822
flow_bps: {'******************************************': 10000, '******************************************': 4968, '******************************************': 2208}
next_flow_bps: {'******************************************': 0, '******************************************': 2208, '******************************************': 9998, '******************************************': 0, '******************************************': 2822}
next_token_balance: {'******************************************': 39114873, '******************************************': 177263476, '******************************************': 0, '******************************************': 11980262879406199430}
--------------------------------

Fill 6/6: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 28.22%
  Available: 11980262879406199430, Allocated: 11980262879406198784
  Output: 50013304

Final output for ******************************************: 177263476
Starting sequential API-based simulation for allocation_usdt-cbbtc_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 7
Starting sequential simulation with 7 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1742
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0}
next_token_balance: {}
--------------------------------

Fill 1/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 17.42%
  Available: 200000000000, Allocated: 34840000000
  Output: 34835700331
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2574
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1742}
next_token_balance: {'******************************************': 34835700331}
--------------------------------

Fill 2/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 25.74%
  Available: 200000000000, Allocated: 51480000000
  Output: 10927737966736004434
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1526
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1742, '******************************************': 2574}
next_token_balance: {'******************************************': 34835700331, '******************************************': 10927737966736004434}
--------------------------------

Fill 3/7: ****************************************** -> ******************************************
  Pool: 0xc7bbec68d12a0d1830360f8ec58fa599ba1b0e9b
  Source: Uniswap_V3
  Proportion: 15.26%
  Available: 200000000000, Allocated: 30520000000
  Output: 6477030397948567578
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 4156
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1742, '******************************************': 4100}
next_token_balance: {'******************************************': 34835700331, '******************************************': 17404768364684572012}
--------------------------------

Fill 4/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 41.56%
  Available: 200000000000, Allocated: 83120000000
  Output: 73644421
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1742
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1742, '******************************************': 4100, '******************************************': 4156}
next_token_balance: {'******************************************': 34835700331, '******************************************': 17404768364684572012, '******************************************': 73644421}
--------------------------------

Fill 5/7: ****************************************** -> ******************************************
  Pool: 0x9a772018fbd77fcd2d25657e5c547baff3fd7d16
  Source: Uniswap_V3
  Proportion: 17.42%
  Available: 34835700331, Allocated: 34835700331
  Output: 30867398
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 4100
flow_bps: {'******************************************': 10000, '******************************************': 1742}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 4100, '******************************************': 5898}
next_token_balance: {'******************************************': 0, '******************************************': 17404768364684572012, '******************************************': 104511819}
--------------------------------

Fill 6/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 41.00%
  Available: 17404768364684572012, Allocated: 17404768364684572672
  Output: 72644036
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 10000
flow_bps: {'******************************************': 10000, '******************************************': 1742, '******************************************': 4100}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 0, '******************************************': 9998}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 177155855}
--------------------------------

Fill 7/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 100.00%
  Available: 177155855, Allocated: 177191293
  Output: 177323494

Final output for ******************************************: 177323494
Starting sequential API-based simulation for allocation_usdt-cbbtc_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 7
Starting sequential simulation with 7 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1742
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0}
next_token_balance: {}
--------------------------------

Fill 1/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 17.42%
  Available: 200000000000, Allocated: 34840000000
  Output: 34835700331
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 2574
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1742}
next_token_balance: {'******************************************': 34835700331}
--------------------------------

Fill 2/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 25.74%
  Available: 200000000000, Allocated: 51480000000
  Output: 10927737966736004434
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1526
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1742, '******************************************': 2574}
next_token_balance: {'******************************************': 34835700331, '******************************************': 10927737966736004434}
--------------------------------

Fill 3/7: ****************************************** -> ******************************************
  Pool: 0xc7bbec68d12a0d1830360f8ec58fa599ba1b0e9b
  Source: Uniswap_V3
  Proportion: 15.26%
  Available: 200000000000, Allocated: 30520000000
  Output: 6477030397948567578
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 4156
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1742, '******************************************': 4100}
next_token_balance: {'******************************************': 34835700331, '******************************************': 17404768364684572012}
--------------------------------

Fill 4/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 41.56%
  Available: 200000000000, Allocated: 83120000000
  Output: 73644421
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1742
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1742, '******************************************': 4100, '******************************************': 4156}
next_token_balance: {'******************************************': 34835700331, '******************************************': 17404768364684572012, '******************************************': 73644421}
--------------------------------

Fill 5/7: ****************************************** -> ******************************************
  Pool: 0x9a772018fbd77fcd2d25657e5c547baff3fd7d16
  Source: Uniswap_V3
  Proportion: 17.42%
  Available: 34835700331, Allocated: 34835700331
  Output: 30867398
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 4100
flow_bps: {'******************************************': 10000, '******************************************': 1742}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 4100, '******************************************': 5898}
next_token_balance: {'******************************************': 0, '******************************************': 17404768364684572012, '******************************************': 104511819}
--------------------------------

Fill 6/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 41.00%
  Available: 17404768364684572012, Allocated: 17404768364684572672
  Output: 72644036
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 10000
flow_bps: {'******************************************': 10000, '******************************************': 1742, '******************************************': 4100}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 0, '******************************************': 9998}
next_token_balance: {'******************************************': 0, '******************************************': 0, '******************************************': 177155855}
--------------------------------

Fill 7/7: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 100.00%
  Available: 177155855, Allocated: 177191293
  Output: 177323494

Final output for ******************************************: 177323494
Starting sequential API-based simulation for allocation_USDT-CBBTC_200000000000
From: ******************************************, To: ******************************************, Amount: 200000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 200000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 10000
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 100.00%
  Available: 200000000000, Allocated: 200000000000
  Output: 200142044425
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 10000
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 10000}
next_token_balance: {'******************************************': 200142044425}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: 0x99ac8ca7087fa4a2a1fb6357269965a2014abc35
  Source: Uniswap_V3
  Proportion: 100.00%
  Available: 200142044425, Allocated: 200142044425
  Output: 177401570
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 10000
flow_bps: {'******************************************': 10000, '******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 0, '******************************************': 10000}
next_token_balance: {'******************************************': 0, '******************************************': 177401570}
--------------------------------

Fill 3/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 100.00%
  Available: 177401570, Allocated: 177401570
  Output: 177401723

Final output for ******************************************: 177401723
