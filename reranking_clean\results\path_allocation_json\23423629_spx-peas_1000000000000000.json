{"blockNumber": 23423629, "fromAmount": "1000000000000000", "from": "******************************************", "toAmount": "387244015931296694358911", "to": "******************************************", "route": {"fills": [{"from": "******************************************", "to": "******************************************", "pool": "0x52c77b0cb827afbad022e6d6caf2c44452edbc39", "source": "Uniswap_V2", "proportionBps": "7775"}, {"from": "******************************************", "to": "******************************************", "pool": "0x7c706586679af2ba6d1a9fc2da9c6af59883fdd3", "source": "Uniswap_V3", "proportionBps": "327"}, {"from": "******************************************", "to": "******************************************", "pool": "0x52c77b0cb827afbad022e6d6caf2c44452edbc39", "source": "Uniswap_V2", "proportionBps": "1896"}, {"from": "******************************************", "to": "******************************************", "pool": "0x0d4a11d5eeaac28ec3f61d100daf4d40471f1852", "source": "Uniswap_V2", "proportionBps": "396"}, {"from": "******************************************", "to": "******************************************", "pool": "0x11b815efb8f581194ae79006d24e0d814b7697f6", "source": "Uniswap_V3", "proportionBps": "596"}, {"from": "******************************************", "to": "******************************************", "pool": "0xc7bbec68d12a0d1830360f8ec58fa599ba1b0e9b", "source": "Uniswap_V3", "proportionBps": "556"}, {"from": "******************************************", "to": "******************************************", "pool": "0x4e68ccd3e89f51c3074ca5072bbac773960dfa36", "source": "Uniswap_V3", "proportionBps": "1165"}, {"from": "******************************************", "to": "******************************************", "pool": "0xbb2b8038a1640196fbe3e38816f3e67cba72d940", "source": "Uniswap_V2", "proportionBps": "297"}, {"from": "******************************************", "to": "******************************************", "pool": "0x4585fe77225b41b697c938b018e2ac67ac5a20c0", "source": "Uniswap_V3", "proportionBps": "1005"}, {"from": "******************************************", "to": "******************************************", "pool": "0x7a415b19932c0105c82fdb6b720bb01b0cc2cae3", "source": "Uniswap_V3", "proportionBps": "371"}, {"from": "******************************************", "to": "******************************************", "pool": "0x8ad599c3a0ff1de082011efddc58f1908eb6e6d8", "source": "Uniswap_V3", "proportionBps": "1499"}, {"from": "******************************************", "to": "******************************************", "pool": "0xe0554a476a092703abdb3ef35c80e0d76d32939f", "source": "Uniswap_V3", "proportionBps": "576"}, {"from": "******************************************", "to": "******************************************", "pool": "0x88e6a0c2ddd26feeb64f039a2c41296fcb3f5640", "source": "Uniswap_V3", "proportionBps": "1636"}, {"from": "******************************************", "to": "******************************************", "pool": "0x5777d92f208679db4b9778590fa3cab3ac9e2168", "source": "Uniswap_V3", "proportionBps": "3712"}, {"from": "******************************************", "to": "******************************************", "pool": "0x48da0965ab2d2cbf1c17c09cfb5cbe67ad5b1406", "source": "Uniswap_V3", "proportionBps": "2715"}, {"from": "******************************************", "to": "******************************************", "pool": "0xcbcdf9626bc03e24f779434178a73a0b4bad62ed", "source": "Uniswap_V3", "proportionBps": "1303"}, {"from": "******************************************", "to": "******************************************", "pool": "0x202a6012894ae5c288ea824cbc8a9bfb26a49b93", "source": "Uniswap_V3", "proportionBps": "371"}, {"from": "******************************************", "to": "******************************************", "pool": "******************************************", "source": "Uniswap_V3", "proportionBps": "3572"}, {"from": "******************************************", "to": "******************************************", "pool": "******************************************", "source": "Uniswap_V3", "proportionBps": "6427"}], "tokens": [{"address": "******************************************", "symbol": "PEAS"}, {"address": "******************************************", "symbol": "WBTC"}, {"address": "******************************************", "symbol": "DAI"}, {"address": "******************************************", "symbol": "USDC"}, {"address": "******************************************", "symbol": "WETH"}, {"address": "******************************************", "symbol": "weETH"}, {"address": "******************************************", "symbol": "USDT"}, {"address": "******************************************", "symbol": "SPX"}]}}