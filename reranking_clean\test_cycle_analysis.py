#!/usr/bin/env python3
"""
Analyze cycle arbitrage potential from SPX-PEAS allocation cycles.

This script analyzes the cycles found in the SPX-PEAS benchmark allocation
and their pool states to determine if they could represent profitable arbitrage opportunities.
"""

import json
import os
import sys

def load_pools_data(block_number):
    """Load V3 and V2 pools data for the given block."""
    v3_pools_file = f"data/prices/block-{block_number}/filtered-v3pools.json"
    v2_pools_file = f"data/prices/block-{block_number}/filtered-v2pools.json"

    if not os.path.exists(v3_pools_file):
        print(f"V3 pools file not found: {v3_pools_file}")
        return None, None

    if not os.path.exists(v2_pools_file):
        print(f"V2 pools file not found: {v2_pools_file}")
        return None, None

    try:
        with open(v3_pools_file, "r", encoding="utf-8") as file:
            v3_pools_data = json.load(file)
        with open(v2_pools_file, "r", encoding="utf-8") as file:
            v2_pools_data = json.load(file)

        print(f"Loaded {len(v3_pools_data)} V3 pools and {len(v2_pools_data)} V2 pools")
        return v3_pools_data, v2_pools_data

    except Exception as e:
        print(f"Error loading pools data: {e}")
        return None, None

def build_pool_lookup_tables(v3_pools_data, v2_pools_data):
    """Build lookup tables for pools by address."""
    v3_pool_map = {p["poolAddress"].lower(): p for p in v3_pools_data}
    v2_pool_map = {p["poolAddress"].lower(): p for p in v2_pools_data}
    return v3_pool_map, v2_pool_map

def analyze_cycle_pools(pool_map_v3, pool_map_v2, cycle_info):
    """
    Analyze the pools in a cycle to understand the potential arbitrage structure.
    """
    print(f"\nAnalyzing cycle structure:")

    cycle_analysis = {
        "pools": [],
        "is_valid": True,
        "total_fees": 0,
        "pool_count": len(cycle_info)
    }

    for i, (from_token, to_token, pool_address, source) in enumerate(cycle_info):
        print(f"\n  Step {i+1}: {from_token[:6]}...{from_token[-4:]} -> {to_token[:6]}...{to_token[-4:]}")
        print(f"    Pool: {pool_address}")
        print(f"    Source: {source}")

        step_analysis = {
            "step": i+1,
            "from_token": from_token,
            "to_token": to_token,
            "pool_address": pool_address,
            "source": source,
            "pool_found": False,
            "fee_tier": None,
            "liquidity": None,
            "price_info": None
        }

        try:
            if source == "Uniswap_V3":
                pool_data = pool_map_v3.get(pool_address.lower())
                if not pool_data:
                    print(f"    [!] V3 pool not found: {pool_address}")
                    cycle_analysis["is_valid"] = False
                    step_analysis["pool_found"] = False
                else:
                    step_analysis["pool_found"] = True
                    pool_static = pool_data["poolState"]["poolStaticInfo"]
                    pool_state = pool_data["poolState"]["poolState"]

                    step_analysis["fee_tier"] = int(pool_static.get("swapFee", 0))
                    step_analysis["liquidity"] = int(pool_state.get("liquidity", 0))

                    # Extract price information
                    sqrt_price = pool_state.get("sqrtPriceX96", "0")
                    current_tick = int(pool_state.get("currentTick", 0))

                    step_analysis["price_info"] = {
                        "sqrt_price": sqrt_price,
                        "current_tick": current_tick,
                        "token0": pool_static["token0"],
                        "token1": pool_static["token1"],
                        "token0_symbol": pool_static["token0Symbol"],
                        "token1_symbol": pool_static["token1Symbol"]
                    }

                    cycle_analysis["total_fees"] += step_analysis["fee_tier"]

                    print(f"    [+] V3 Pool found")
                    print(f"       Fee tier: {step_analysis['fee_tier']} ({step_analysis['fee_tier']/10000:.2f}%)")
                    print(f"       Liquidity: {step_analysis['liquidity']:,}")
                    print(f"       Current tick: {current_tick}")
                    print(f"       Token pair: {pool_static['token0Symbol']}-{pool_static['token1Symbol']}")

            elif source == "Uniswap_V2":
                pool_data = pool_map_v2.get(pool_address.lower())
                if not pool_data:
                    print(f"    [!] V2 pool not found: {pool_address}")
                    cycle_analysis["is_valid"] = False
                    step_analysis["pool_found"] = False
                else:
                    step_analysis["pool_found"] = True
                    pool_static = pool_data["poolState"]["poolStaticInfo"]
                    pool_state = pool_data["poolState"]["poolState"]

                    step_analysis["fee_tier"] = 300  # V2 has 0.3% fee
                    step_analysis["liquidity"] = {
                        "reserve0": int(pool_state.get("tokenBalance0", 0)),
                        "reserve1": int(pool_state.get("tokenBalance1", 0))
                    }

                    step_analysis["price_info"] = {
                        "reserve0": step_analysis["liquidity"]["reserve0"],
                        "reserve1": step_analysis["liquidity"]["reserve1"],
                        "token0": pool_static["token0"],
                        "token1": pool_static["token1"],
                        "token0_symbol": pool_static["token0Symbol"],
                        "token1_symbol": pool_static["token1Symbol"]
                    }

                    cycle_analysis["total_fees"] += step_analysis["fee_tier"]

                    print(f"    [+] V2 Pool found")
                    print(f"       Fee tier: {step_analysis['fee_tier']} ({step_analysis['fee_tier']/10000:.2f}%)")
                    print(f"       Reserve0: {step_analysis['liquidity']['reserve0']:,}")
                    print(f"       Reserve1: {step_analysis['liquidity']['reserve1']:,}")
                    print(f"       Token pair: {pool_static['token0Symbol']}-{pool_static['token1Symbol']}")
            else:
                print(f"    [!] Unknown source: {source}")
                cycle_analysis["is_valid"] = False

        except Exception as e:
            print(f"    [!] Analysis error: {e}")
            cycle_analysis["is_valid"] = False

        cycle_analysis["pools"].append(step_analysis)

    # Calculate total fee burden
    total_fee_percent = cycle_analysis["total_fees"] / 10000
    print(f"\nCycle Summary:")
    print(f"   Valid cycle: {'YES' if cycle_analysis['is_valid'] else 'NO'}")
    print(f"   Total steps: {cycle_analysis['pool_count']}")
    print(f"   Total fee burden: {total_fee_percent:.4f}% ({cycle_analysis['total_fees']} bps)")
    print(f"   Break-even requirement: Price difference must exceed {total_fee_percent:.4f}%")

    return cycle_analysis

def test_spx_peas_cycles():
    """Test the cycles from SPX-PEAS allocation for arbitrage."""
    block_number = 23423629

    print("=" * 80)
    print("TESTING SPX-PEAS CYCLES FOR ARBITRAGE")
    print("=" * 80)

    # Load pools data
    v3_pools_data, v2_pools_data = load_pools_data(block_number)
    if not v3_pools_data or not v2_pools_data:
        return

    v3_pool_map, v2_pool_map = build_pool_lookup_tables(v3_pools_data, v2_pools_data)

    # Define the cycles from SPX-PEAS allocation
    cycles = [
        {
            "name": "WETH -> WBTC -> WETH",
            "steps": [
                ("******************************************", "******************************************",
                 "******************************************", "Uniswap_V3"),
                ("******************************************", "******************************************",
                 "******************************************", "Uniswap_V3")
            ]
        },
        {
            "name": "WETH -> weETH -> WETH",
            "steps": [
                ("******************************************", "******************************************",
                 "******************************************", "Uniswap_V3"),
                ("******************************************", "******************************************",
                 "******************************************", "Uniswap_V3")
            ]
        }
    ]

    for cycle in cycles:
        print(f"\n{'='*60}")
        print(f"Testing Cycle: {cycle['name']}")
        print(f"{'='*60}")

        # Analyze the cycle structure
        analysis = analyze_cycle_pools(v3_pool_map, v2_pool_map, cycle["steps"])

        if analysis and analysis["is_valid"]:
            print(f"\nARBITRAGE POTENTIAL ANALYSIS:")
            print(f"   Cycle is valid: [+] All pools found")
            print(f"   Fee burden: {analysis['total_fees'] / 10000:.4f}%")
            print(f"   For profitability, the cycle must generate > {analysis['total_fees'] / 10000:.4f}% return")

            # Additional analysis based on the proportions from the allocation
            if "WETH -> WBTC -> WETH" in cycle["name"]:
                # From allocation: WETH->WBTC=10.05%, WBTC->WETH=13.03%
                print(f"   Allocation shows: 10.05% -> 13.03% flow proportions")
                print(f"   This suggests WBTC was undervalued relative to WETH")
                print(f"   Potential arbitrage: Buy WBTC with WETH, sell WBTC back for more WETH")
            elif "WETH -> weETH -> WETH" in cycle["name"]:
                # From allocation: WETH->weETH=3.71%, weETH->WETH=3.71%
                print(f"   Allocation shows: 3.71% -> 3.71% flow proportions")
                print(f"   This suggests balanced flows - less likely to be profitable arbitrage")
                print(f"   May be more about maintaining liquidity balance")
        else:
            print(f"\n[!] CYCLE INVALID: Cannot analyze arbitrage potential")

    print(f"\n{'='*80}")
    print("CYCLE ARBITRAGE TESTING COMPLETED")
    print(f"{'='*80}")

if __name__ == "__main__":
    test_spx_peas_cycles()