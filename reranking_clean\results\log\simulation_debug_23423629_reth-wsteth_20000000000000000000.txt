Starting sequential API-based simulation for 23423629_reth-wsteth_20000000000000000000
From: ******************************************, To: ******************************************, Amount: 20000000000000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 20000000000000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1521
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 15.21%
  Available: 20000000000000000000, Allocated: 3042000000000000512
  Output: 3468280186477003910
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 8478
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1521}
next_token_balance: {'******************************************': 3468280186477003910}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: 0x553e9c493678d8606d6a5ba284643db2110df823
  Source: Uniswap_V3
  Proportion: 84.78%
  Available: 20000000000000000000, Allocated: 16956000000000000000
  Output: 19344971425879892070
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 10000
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 9999}
next_token_balance: {'******************************************': 22813251612356895980}
--------------------------------

Fill 3/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 100.00%
  Available: 22813251612356895980, Allocated: 22815533165673459712
  Output: 18803115397026505739

Final output for ******************************************: 18803115397026505739
Starting sequential API-based simulation for 23423629_reth-wsteth_20000000000000000000
From: ******************************************, To: ******************************************, Amount: 20000000000000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 20000000000000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1521
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 15.21%
  Available: 20000000000000000000, Allocated: 3042000000000000512
  Output: 3420449960916780437
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 8478
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1521}
next_token_balance: {'******************************************': 3420449960916780437}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: 0x553e9c493678d8606d6a5ba284643db2110df823
  Source: Uniswap_V3
  Proportion: 84.78%
  Available: 20000000000000000000, Allocated: 16956000000000000000
  Output: 19272901212435331998
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 10000
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 9999}
next_token_balance: {'******************************************': 22693351173352112435}
--------------------------------

Fill 3/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 100.00%
  Available: 22693351173352112435, Allocated: 22695620735425654784
  Output: 18768492094583988014

Final output for ******************************************: 18768492094583988014
Starting sequential API-based simulation for 23423629_reth-wsteth_20000000000000000000
From: ******************************************, To: ******************************************, Amount: 20000000000000000000
Number of fills: 3
Starting sequential simulation with 3 fills
Initial balance: ****************************************** = 20000000000000000000
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 1521
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0}
next_token_balance: {}
--------------------------------

Fill 1/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 15.21%
  Available: 20000000000000000000, Allocated: 3042000000000000512
  Output: 3468280186477003910
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 8478
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 1521}
next_token_balance: {'******************************************': 3468280186477003910}
--------------------------------

Fill 2/3: ****************************************** -> ******************************************
  Pool: 0x553e9c493678d8606d6a5ba284643db2110df823
  Source: Uniswap_V3
  Proportion: 84.78%
  Available: 20000000000000000000, Allocated: 16956000000000000000
  Output: 19344971425879892070
current_token: ******************************************
token_in_addr: ******************************************
token_out_addr: ******************************************
proportion_bps: 10000
flow_bps: {'******************************************': 10000}
next_flow_bps: {'******************************************': 0, '******************************************': 9999}
next_token_balance: {'******************************************': 22813251612356895980}
--------------------------------

Fill 3/3: ****************************************** -> ******************************************
  Pool: ******************************************
  Source: Uniswap_V3
  Proportion: 100.00%
  Available: 22813251612356895980, Allocated: 22815533165673459712
  Output: 18803115397026505739

Final output for ******************************************: 18803115397026505739
