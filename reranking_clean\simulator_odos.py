import json
import requests
import copy
import os
import glob
import time
import subprocess
from decimal import Decimal
from typing import Dict, List, Any, Optional

# API Configuration
API_BASE_URL = "http://127.0.0.1:8080"


def generate_curl_command(request_data: Dict[str, Any], endpoint: str) -> str:
    """
    Generate a curl command for manual testing.
    """
    json_data = json.dumps(request_data, indent=2)

    # Create a temporary file for the JSON data
    import tempfile
    import os

    # Create temp directory if it doesn't exist
    os.makedirs("temp", exist_ok=True)

    # Generate a unique filename
    import uuid

    temp_filename = f"temp/request_{uuid.uuid4().hex[:8]}.json"

    # Write JSON to file
    with open(temp_filename, "w") as f:
        f.write(json_data)

    curl_command = f"""curl -sS {API_BASE_URL}/{endpoint} \\
  -H "Content-Type: application/json" \\
  -d @{temp_filename}"""

    return curl_command, temp_filename


def execute_curl_request(request_data: Dict[str, Any], endpoint: str) -> Dict[str, Any]:
    """
    Make HTTP request using requests library with the same format as curl.
    """
    try:
        # Convert request data to JSON string (like curl does)
        json_data = json.dumps(request_data)

        # Make the request with data parameter (like curl -d)
        response = requests.post(
            f"{API_BASE_URL}/{endpoint}",
            headers={"Content-Type": "application/json"},
            data=json_data,  # Use data instead of json parameter
            timeout=30,
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(
                f"HTTP request failed with status {response.status_code}: {response.text}"
            )

    except Exception as e:
        raise Exception(f"Request execution error: {str(e)}")


def format_pool_state_for_api(pool_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format pool state for API calls according to the README.md specifications.
    Filters ticks to only include initialized ones with non-zero liquidity_net,
    sorts them in ascending order, and uses actual sqrt_price from tick data.
    """
    pool_state = pool_data["poolState"]["poolState"]
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]

    # Filter and format ticks
    formatted_ticks = []
    if "tickBitMap" in pool_state:
        for tick_info in pool_state["tickBitMap"]:
            tick_idx = int(tick_info[0])
            tick_data = tick_info[1]

            # Only include ticks that are initialized and have non-zero liquidity_net
            if (
                tick_data.get("initialized", False)
                and tick_data.get("liquidityNet", "0") != "0"
            ):
                # Use actual sqrt_price from tick data if available, otherwise use "0"
                tick_sqrt_price = tick_data.get("sqrt_price", "0")
                formatted_tick = {
                    "tick": tick_idx,
                    "liquidity_net": tick_data["liquidityNet"],
                    "sqrt_price": tick_sqrt_price,
                }
                formatted_ticks.append(formatted_tick)

    # Sort ticks in ascending order
    formatted_ticks.sort(key=lambda x: x["tick"])

    # Format the API request structure
    api_pool_data = {
        "liquidity": int(
            pool_state["liquidity"]
        ),  # Keep as integer like in working example
        "sqrt_price": str(pool_state["sqrtPriceX96"]),  # Keep as string
        "fee": int(pool_static_info["swapFee"]),
        "tick": int(pool_state["currentTick"]),
        "ticks": formatted_ticks,
    }

    # Validate and align current tick
    if formatted_ticks:
        tick_values = [tick["tick"] for tick in formatted_ticks]
        min_tick = min(tick_values)
        max_tick = max(tick_values)
        current_tick = int(pool_state["currentTick"])

        tick_spacing = int(pool_static_info.get("tickSpacing", 1))
        # fee = int(pool_static_info["swapFee"])
        # if fee == 100:  # 0.01%
        # tick_spacing = 1
        # elif fee == 500:  # 0.05%
        # tick_spacing = 10
        # elif fee == 3000:  # 0.3%
        # tick_spacing = 60
        # elif fee == 10000:  # 1%
        # tick_spacing = 200
        # else:
        # tick_spacing = 1  # Default

        # Align current tick to tick spacing
        aligned_tick = (current_tick // tick_spacing) * tick_spacing
        if aligned_tick != current_tick:
            print(
                f"Warning: Current tick {current_tick} not aligned to spacing {tick_spacing}"
            )
            print(f"Aligned tick: {aligned_tick}")
            api_pool_data["tick"] = aligned_tick

        # Check if aligned tick is within range
        if aligned_tick < min_tick or aligned_tick > max_tick:
            print(
                f"Warning: Aligned tick {aligned_tick} is outside tick range [{min_tick}, {max_tick}]"
            )
            # Try to find the closest valid tick
            closest_tick = min(tick_values, key=lambda x: abs(x - aligned_tick))
            print(f"Using closest tick: {closest_tick}")
            api_pool_data["tick"] = closest_tick

    return api_pool_data


def simulate_swap_zero_for_one_v3(
    pool_data: Dict[str, Any], amount_in: int, log_collect=None
) -> Dict[str, Any]:
    """
    Simulate a Uniswap V3 exactInput swap (token0 -> token1) using API.
    """
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]

    # Format pool state for API
    api_pool_data = format_pool_state_for_api(pool_data)

    # Prepare API request
    request_data = {
        "amount_in": str(amount_in),
        "token_in": pool_static_info["token0"],
        "token_in_decimals": int(pool_static_info["token0Decimals"]),
        "token_out": pool_static_info["token1"],
        "token_out_decimals": int(pool_static_info["token1Decimals"]),
        "liquidity": api_pool_data["liquidity"],  # Keep as integer
        "sqrt_price": api_pool_data["sqrt_price"],  # Keep as string
        "fee": api_pool_data["fee"],
        "tick": api_pool_data["tick"],
        "ticks": api_pool_data["ticks"],
    }
    curl_cmd, temp_file = generate_curl_command(request_data, "get_amount_out")
    print("\nCurl command for manual testing:")
    print(curl_cmd)
    print(f"\nJSON data saved to: {temp_file}")
    print("\n" + "=" * 80)

    try:
        while True:
            response = requests.post(
                f"{API_BASE_URL}/get_amount_out",
                headers={"Content-Type": "application/json"},
                json=request_data,
                timeout=30,
            )
            # print(response)
            # print("--------------------------------")

            if response.status_code == 200:
                result = response.json()

                return {
                    "amount": int(result["amount"]),
                    "token0_consumed": amount_in,
                    "fees_collected": 0,  # API doesn't return fees separately
                    "new_pool_state": "None",
                    "gas_used": int(result.get("gas", 0)),
                }
            elif "Ticks exceeded" in response.text:
                amount_in = int(amount_in * 0.99)
                request_data["amount_in"] = str(amount_in)
                if log_collect:
                    log_collect(f"Amount in decreased to: {amount_in}")
                print(f"Amount in decreased to: {amount_in}")
                continue
            else:
                print(
                    f"API call failed with status {response.status_code}: {response.text}"
                )
                raise Exception(
                    f"API call failed with status {response.status_code}: {response.text}"
                )

    except Exception as e:
        raise Exception(f"V3 API simulation error: {str(e)}")


def simulate_swap_one_for_zero_v3(
    pool_data: Dict[str, Any], amount_in: int, log_collect=None
) -> Dict[str, Any]:
    """
    Simulate a Uniswap V3 exactInput swap (token1 -> token0) using API.
    """
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]

    # Format pool state for API
    api_pool_data = format_pool_state_for_api(pool_data)

    # Prepare API request
    request_data = {
        "amount_in": str(amount_in),
        "token_in": pool_static_info["token1"],
        "token_in_decimals": int(pool_static_info["token1Decimals"]),
        "token_out": pool_static_info["token0"],
        "token_out_decimals": int(pool_static_info["token0Decimals"]),
        "liquidity": api_pool_data["liquidity"],  # Keep as integer
        "sqrt_price": api_pool_data["sqrt_price"],  # Keep as string
        "fee": api_pool_data["fee"],
        "tick": api_pool_data["tick"],
        "ticks": api_pool_data["ticks"],
    }

    curl_cmd, temp_file = generate_curl_command(request_data, "get_amount_out")
    print("\nCurl command for manual testing:")
    print(curl_cmd)
    print(f"\nJSON data saved to: {temp_file}")
    print("\n" + "=" * 80)

    try:

        while True:
            response = requests.post(
                f"{API_BASE_URL}/get_amount_out",
                headers={"Content-Type": "application/json"},
                json=request_data,
                timeout=30,
            )
            # print(response)
            # print("--------------------------------")

            if response.status_code == 200:
                result = response.json()

                return {
                    "amount": int(result["amount"]),
                    "token1_consumed": amount_in,
                    "fees_collected": 0,  # API doesn't return fees separately
                    "new_pool_state": "None",
                    "gas_used": int(result.get("gas", 0)),
                }
            elif "Ticks exceeded" in response.text:
                amount_in = int(amount_in * 0.99)
                request_data["amount_in"] = str(amount_in)
                if log_collect:
                    log_collect(f"Amount in decreased to: {amount_in}")
                print(f"Amount in decreased to: {amount_in}")
                continue
            else:
                print(
                    f"API call failed with status {response.status_code}: {response.text}"
                )
                raise Exception(
                    f"API call failed with status {response.status_code}: {response.text}"
                )

    except Exception as e:
        raise Exception(f"V3 API simulation error: {str(e)}")


def simulate_swap_zero_for_one_v3_with_update(
    pool_data: Dict[str, Any], amount_in: int
) -> Dict[str, Any]:
    """
    Simulate a Uniswap V3 exactInput swap (token0 -> token1) using API with pool state update.
    Directly modifies the original pool_data.
    """
    print("test")
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]

    # Format pool state for API
    # print("test1")
    api_pool_data = format_pool_state_for_api(pool_data)
    # print("test2")
    # print(amount_in)

    # Prepare API request
    request_data = {
        "amount_in": str(amount_in),
        "token_in": pool_static_info["token0"],
        "token_in_decimals": int(pool_static_info["token0Decimals"]),
        "token_out": pool_static_info["token1"],
        "token_out_decimals": int(pool_static_info["token1Decimals"]),
        "liquidity": api_pool_data["liquidity"],  # Keep as integer
        "sqrt_price": api_pool_data["sqrt_price"],  # Keep as string
        "fee": api_pool_data["fee"],
        "tick": api_pool_data["tick"],
        "ticks": api_pool_data["ticks"],
    }
    # print("Request data:")
    # print(json.dumps(request_data, indent=2))

    # Generate and print curl command for manual testing
    curl_cmd, temp_file = generate_curl_command(
        request_data, "get_amount_out_with_update"
    )
    print("\nCurl command for manual testing:")
    print(curl_cmd)
    print(f"\nJSON data saved to: {temp_file}")
    print("\n" + "=" * 80)

    try:
        while True:
            # Use requests with curl-like formatting
            print("Executing HTTP request...")

            try:
                response = requests.post(
                    f"{API_BASE_URL}/get_amount_out_with_update",
                    headers={"Content-Type": "application/json"},
                    data=json.dumps(request_data),
                    timeout=30,
                )

                if response.status_code == 200:
                    result = response.json()
                    print(f"HTTP request executed successfully")
                    print("--------------------------------")
                    break
                elif "Ticks exceeded" in response.text:
                    amount_in = int(amount_in * 0.99)
                    request_data["amount_in"] = str(amount_in)
                    print(f"Amount in decreased to: {amount_in}")
                    continue
                else:
                    print(
                        f"API call failed with status {response.status_code}: {response.text}"
                    )
                    raise Exception(
                        f"API call failed with status {response.status_code}: {response.text}"
                    )

            except Exception as e:
                raise Exception(f"Request execution error: {str(e)}")

        # Directly update the original pool_data
        pool_data["poolState"]["poolState"]["sqrtPriceX96"] = result["sqrt_price"]
        pool_data["poolState"]["poolState"]["currentTick"] = str(result["tick"])
        pool_data["poolState"]["poolState"]["liquidity"] = result["liquidity"]

        # Update ticks if provided
        if "ticks" in result:
            # Convert back to the original list format: [[tick_idx, tick_data], ...]
            tick_bitmap = []
            for tick in result["ticks"]:
                tick_data = {
                    "liquidityNet": tick["liquidity_net"],
                    "initialized": True,
                    "sqrt_price": tick["sqrt_price"],
                    "liquidityGross": "0",  # Add missing field with default value
                }
                tick_bitmap.append([tick["tick"], tick_data])
            pool_data["poolState"]["poolState"]["tickBitMap"] = tick_bitmap

        return {
            "amount": int(result["amount"]),
            "token0_consumed": amount_in,
            "fees_collected": 0,  # API doesn't return fees separately
            "gas_used": int(result.get("gas", 0)),
        }

    except Exception as e:
        print(f"V3 API simulation error: {str(e)}")
        print("If the input amount is too large, the API will return an error")
        raise Exception(f"V3 API simulation error: {str(e)}")


def simulate_swap_one_for_zero_v3_with_update(
    pool_data: Dict[str, Any], amount_in: int
) -> Dict[str, Any]:
    """
    Simulate a Uniswap V3 exactInput swap (token1 -> token0) using API with pool state update.
    Directly modifies the original pool_data.
    """
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]

    # Format pool state for API
    api_pool_data = format_pool_state_for_api(pool_data)

    # Prepare API request
    request_data = {
        "amount_in": str(amount_in),
        "token_in": pool_static_info["token1"],
        "token_in_decimals": int(pool_static_info["token1Decimals"]),
        "token_out": pool_static_info["token0"],
        "token_out_decimals": int(pool_static_info["token0Decimals"]),
        "liquidity": api_pool_data["liquidity"],  # Keep as integer
        "sqrt_price": api_pool_data["sqrt_price"],  # Keep as string
        "fee": api_pool_data["fee"],
        "tick": api_pool_data["tick"],
        "ticks": api_pool_data["ticks"],
    }

    # print("Request data:")
    # print(json.dumps(request_data, indent=2))

    # Generate and print curl command for manual testing
    curl_cmd, temp_file = generate_curl_command(
        request_data, "get_amount_out_with_update"
    )
    print("\nCurl command for manual testing:")
    print(curl_cmd)
    print(f"\nJSON data saved to: {temp_file}")
    print("\n" + "=" * 80)

    try:
        while True:
            # Use requests with curl-like formatting
            print("Executing HTTP request...")

            try:
                response = requests.post(
                    f"{API_BASE_URL}/get_amount_out_with_update",
                    headers={"Content-Type": "application/json"},
                    data=json.dumps(request_data),
                    timeout=30,
                )

                if response.status_code == 200:
                    result = response.json()
                    print(f"HTTP request executed successfully")
                    print("--------------------------------")
                    break
                elif "Ticks exceeded" in response.text:
                    amount_in = int(amount_in * 0.99)
                    request_data["amount_in"] = str(amount_in)
                    print(f"Amount in decreased to: {amount_in}")
                    continue
                else:
                    print(
                        f"API call failed with status {response.status_code}: {response.text}"
                    )
                    raise Exception(
                        f"API call failed with status {response.status_code}: {response.text}"
                    )

            except Exception as e:
                raise Exception(f"Request execution error: {str(e)}")

        # Directly update the original pool_data
        pool_data["poolState"]["poolState"]["sqrtPriceX96"] = result["sqrt_price"]
        pool_data["poolState"]["poolState"]["currentTick"] = str(result["tick"])
        pool_data["poolState"]["poolState"]["liquidity"] = result["liquidity"]

        # Update ticks if provided
        if "ticks" in result:
            # Convert back to the original list format: [[tick_idx, tick_data], ...]
            tick_bitmap = []
            for tick in result["ticks"]:
                tick_data = {
                    "liquidityNet": tick["liquidity_net"],
                    "initialized": True,
                    "sqrt_price": tick["sqrt_price"],
                    "liquidityGross": "0",  # Add missing field with default value
                }
                tick_bitmap.append([tick["tick"], tick_data])
            pool_data["poolState"]["poolState"]["tickBitMap"] = tick_bitmap

        return {
            "amount": int(result["amount"]),
            "token1_consumed": amount_in,
            "fees_collected": 0,  # API doesn't return fees separately
            "gas_used": int(result.get("gas", 0)),
        }

    except Exception as e:
        print(f"V3 API simulation error: {str(e)}")
        print("If the input amount is too large, the API will return an error")
        raise Exception(f"V3 API simulation error: {str(e)}")


def format_v2_pool_state_for_api(pool_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format V2 pool state for API calls.
    """
    pool_state = pool_data["poolState"]["poolState"]
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]

    return {
        "reserve0": int(pool_state["tokenBalance0"]),  # Keep as integer for V2
        "reserve1": int(pool_state["tokenBalance1"]),  # Keep as integer for V2
        "token0": pool_static_info["token0"],
        "token1": pool_static_info["token1"],
    }


def simulate_swap_zero_for_one_v2(
    pool_data: Dict[str, Any], amount_in: int
) -> Dict[str, Any]:
    """
    Simulate a Uniswap V2 swap (token0 -> token1) using API.
    """
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]
    pool_state = pool_data["poolState"]["poolState"]

    # Prepare API request
    request_data = {
        "amount_in": str(amount_in),
        "token_in": pool_static_info["token0"],
        "token_in_decimals": int(pool_static_info["token0Decimals"]),
        "token_out": pool_static_info["token1"],
        "token_out_decimals": int(pool_static_info["token1Decimals"]),
        "reserve0": int(pool_state["tokenBalance0"]),  # Keep as integer for V2
        "reserve1": int(pool_state["tokenBalance1"]),  # Keep as integer for V2
    }

    try:
        # Use requests with curl-like formatting
        result = execute_curl_request(request_data, "get_amount_out_v2")

        return {
            "amount": int(result["amount"]),
            "token0_consumed": amount_in,
            "fees_collected": 0,  # API doesn't return fees separately
            "new_pool_state": pool_data,  # V2 doesn't update state in basic call
            "gas_used": int(result.get("gas", 0)),
        }

    except Exception as e:
        raise Exception(f"V2 API simulation error: {str(e)}")


def simulate_swap_one_for_zero_v2(
    pool_data: Dict[str, Any], amount_in: int
) -> Dict[str, Any]:
    """
    Simulate a Uniswap V2 swap (token1 -> token0) using API.
    """
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]
    pool_state = pool_data["poolState"]["poolState"]

    # Prepare API request
    request_data = {
        "amount_in": str(amount_in),
        "token_in": pool_static_info["token1"],
        "token_in_decimals": int(pool_static_info["token1Decimals"]),
        "token_out": pool_static_info["token0"],
        "token_out_decimals": int(pool_static_info["token0Decimals"]),
        "reserve0": int(pool_state["tokenBalance0"]),  # Keep as integer for V2
        "reserve1": int(pool_state["tokenBalance1"]),  # Keep as integer for V2
    }

    try:
        # Use requests with curl-like formatting
        result = execute_curl_request(request_data, "get_amount_out_v2")

        return {
            "amount": int(result["amount"]),
            "token1_consumed": amount_in,
            "fees_collected": 0,  # API doesn't return fees separately
            "new_pool_state": pool_data,  # V2 doesn't update state in basic call
            "gas_used": int(result.get("gas", 0)),
        }

    except Exception as e:
        raise Exception(f"V2 API simulation error: {str(e)}")


def simulate_swap_zero_for_one_v2_with_update(
    pool_data: Dict[str, Any], amount_in: int
) -> Dict[str, Any]:
    """
    Simulate a Uniswap V2 swap (token0 -> token1) using API with pool state update.
    Directly modifies the original pool_data.
    """
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]
    pool_state = pool_data["poolState"]["poolState"]

    # Prepare API request
    request_data = {
        "amount_in": str(amount_in),
        "token_in": pool_static_info["token0"],
        "token_in_decimals": int(pool_static_info["token0Decimals"]),
        "token_out": pool_static_info["token1"],
        "token_out_decimals": int(pool_static_info["token1Decimals"]),
        "reserve0": int(pool_state["tokenBalance0"]),  # Keep as integer for V2
        "reserve1": int(pool_state["tokenBalance1"]),  # Keep as integer for V2
    }

    # print("Request data:")
    # print(json.dumps(request_data, indent=2))

    # Generate and print curl command for manual testing
    curl_cmd, temp_file = generate_curl_command(
        request_data, "get_amount_out_v2_with_update"
    )
    print("\nCurl command for manual testing:")
    print(curl_cmd)
    print(f"\nJSON data saved to: {temp_file}")
    print("\n" + "=" * 80)

    try:
        # Use requests with curl-like formatting
        result = execute_curl_request(request_data, "get_amount_out_v2_with_update")

        # Directly update the original pool_data
        pool_data["poolState"]["poolState"]["tokenBalance0"] = result["reserve0"]
        pool_data["poolState"]["poolState"]["tokenBalance1"] = result["reserve1"]

        return {
            "amount": int(result["amount"]),
            "token0_consumed": amount_in,
            "fees_collected": 0,  # API doesn't return fees separately
            "gas_used": int(result.get("gas", 0)),
        }

    except Exception as e:
        raise Exception(f"V2 API simulation error: {str(e)}")


def simulate_swap_one_for_zero_v2_with_update(
    pool_data: Dict[str, Any], amount_in: int
) -> Dict[str, Any]:
    """
    Simulate a Uniswap V2 swap (token1 -> token0) using API with pool state update.
    Directly modifies the original pool_data.
    """
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]
    pool_state = pool_data["poolState"]["poolState"]

    # Prepare API request
    request_data = {
        "amount_in": str(amount_in),
        "token_in": pool_static_info["token1"],
        "token_in_decimals": int(pool_static_info["token1Decimals"]),
        "token_out": pool_static_info["token0"],
        "token_out_decimals": int(pool_static_info["token0Decimals"]),
        "reserve0": int(pool_state["tokenBalance0"]),  # Keep as integer for V2
        "reserve1": int(pool_state["tokenBalance1"]),  # Keep as integer for V2
    }

    print("Request data:")
    print(json.dumps(request_data, indent=2))

    # Generate and print curl command for manual testing
    curl_cmd, temp_file = generate_curl_command(
        request_data, "get_amount_out_v2_with_update"
    )
    print("\nCurl command for manual testing:")
    print(curl_cmd)
    print(f"\nJSON data saved to: {temp_file}")
    print("\n" + "=" * 80)

    try:
        # Use requests with curl-like formatting
        result = execute_curl_request(request_data, "get_amount_out_v2_with_update")

        # Directly update the original pool_data
        pool_data["poolState"]["poolState"]["tokenBalance0"] = result["reserve0"]
        pool_data["poolState"]["poolState"]["tokenBalance1"] = result["reserve1"]

        return {
            "amount": int(result["amount"]),
            "token1_consumed": amount_in,
            "fees_collected": 0,  # API doesn't return fees separately
            "gas_used": int(result.get("gas", 0)),
        }

    except Exception as e:
        raise Exception(f"V2 API simulation error: {str(e)}")


# Convenience function to determine swap direction and call appropriate function
def simulate_swap_v3(
    pool_data: Dict[str, Any],
    amount_in: int,
    token_in: str,
    with_update: bool = False,
    log_collect=None,
) -> Dict[str, Any]:
    """
    Simulate a V3 swap with automatic direction detection.

    Args:
        pool_data: Pool data dictionary
        amount_in: Input amount
        token_in: Address of the input token
        with_update: Whether to use the with_update endpoint

    Returns:
        Simulation result dictionary
    """
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]
    token0 = pool_static_info["token0"].lower()
    token_in_lower = token_in.lower()

    if token_in_lower == token0:
        if with_update:
            return simulate_swap_zero_for_one_v3_with_update(pool_data, amount_in)
        else:
            return simulate_swap_zero_for_one_v3(
                pool_data, amount_in, log_collect=log_collect
            )
    else:
        if with_update:
            return simulate_swap_one_for_zero_v3_with_update(pool_data, amount_in)
        else:
            return simulate_swap_one_for_zero_v3(
                pool_data, amount_in, log_collect=log_collect
            )


def simulate_swap_v2(
    pool_data: Dict[str, Any], amount_in: int, token_in: str, with_update: bool = False
) -> Dict[str, Any]:
    """
    Simulate a V2 swap with automatic direction detection.

    Args:
        pool_data: Pool data dictionary
        amount_in: Input amount
        token_in: Address of the input token
        with_update: Whether to use the with_update endpoint

    Returns:
        Simulation result dictionary
    """
    pool_static_info = pool_data["poolState"]["poolStaticInfo"]
    token0 = pool_static_info["token0"].lower()
    token_in_lower = token_in.lower()

    if token_in_lower == token0:
        if with_update:
            return simulate_swap_zero_for_one_v2_with_update(pool_data, amount_in)
        else:
            return simulate_swap_zero_for_one_v2(pool_data, amount_in)
    else:
        if with_update:
            return simulate_swap_one_for_zero_v2_with_update(pool_data, amount_in)
        else:
            return simulate_swap_one_for_zero_v2(pool_data, amount_in)


def single_hop_simulation_api(
    token_in_addr: str,
    token_out_addr: str,
    pool_addr: str,
    pool_type: str,
    amount_in: int,
    v3_pools_data: list,
    v2_pools_data: list,
    log_collect=None,
    with_update=True,
) -> int:
    """Perform a single hop simulation using API for both V2 and V3."""
    if amount_in == 0:
        return 0

    # Determine if it's V2 or V3
    pool_type_lower = pool_type.lower()
    amount_out = 0

    # Log helper
    def logger(msg):
        if log_collect:
            log_collect(msg)

    if pool_type_lower == "v3" or "uniswap_v3" in pool_type_lower:
        # Find pool data
        found_pool = None
        for pool in v3_pools_data:
            candidate = None
            if "poolState" in pool and "poolStaticInfo" in pool["poolState"]:
                candidate = pool["poolState"]["poolStaticInfo"].get("poolAddress", "")
            elif "address" in pool:
                candidate = pool["address"]
            if candidate and candidate.lower() == pool_addr.lower():
                found_pool = pool
                break

        if not found_pool:
            if logger:
                logger(f"  Error: V3 pool {pool_addr} not found in data, returning 0.")
            return 0

        # Use API simulation with update
        try:
            print("amount_in: ", amount_in)
            result = simulate_swap_v3(
                found_pool,
                amount_in,
                token_in_addr,
                with_update=with_update,
                log_collect=log_collect,
            )
            amount_out = result["amount"]
            print("amount_out: ", amount_out)
            print("--------------------------------")

        except Exception as e:
            if logger:
                logger(f"    V3 API simulation error: {e}")
            amount_out = 0

    elif pool_type_lower == "v2" or "uniswap_v2" in pool_type_lower:
        # Find pool data
        found_pool = None
        for pool in v2_pools_data:
            candidate = None
            if "poolState" in pool and "poolStaticInfo" in pool["poolState"]:
                candidate = pool["poolState"]["poolStaticInfo"].get("poolAddress", "")
            elif "poolAddress" in pool:
                candidate = pool.get("poolAddress", "")

            if candidate and candidate.lower() == pool_addr.lower():
                found_pool = pool
                break

        if not found_pool:
            if logger:
                logger(f"  Error: V2 pool {pool_addr} not found in data, returning 0.")
            return 0

        # Use API simulation with update
        try:
            result = simulate_swap_v2(
                found_pool, amount_in, token_in_addr, with_update=with_update
            )
            amount_out = result["amount"]
            print("amount_out: ", amount_out)
            print("--------------------------------")

        except Exception as e:
            if logger:
                logger(f"    V2 API simulation error: {e}")
            amount_out = 0

    else:
        if logger:
            logger(f"  Error: Unknown pool type {pool_type}, returning 0.")
        amount_out = 0

    return amount_out


def simulate_single_allocation_file(
    file_path: str, v3_pools_file: str, v2_pools_file: str
) -> dict:
    """
    Simulate a single allocation file and return the result.

    Args:
        file_path: Path to the allocation JSON file
        v3_pools_file: Path to V3 pools JSON file
        v2_pools_file: Path to V2 pools JSON file

    Returns:
        Dictionary with simulation result or error
    """
    try:
        # Load pools data for this simulation
        try:
            with open(v3_pools_file, "r", encoding="utf-8") as file:
                v3_pools_data = json.load(file)
            with open(v2_pools_file, "r", encoding="utf-8") as file:
                v2_pools_data = json.load(file)
        except Exception as e:
            return {
                "file": os.path.basename(file_path),
                "status": "failed",
                "error": f"Error loading pools data: {str(e)}",
            }

        # Load the allocation file
        with open(file_path, "r", encoding="utf-8") as f:
            allocation_data = json.load(f)

        # Check if it's a valid allocation JSON
        if "fromAmount" not in allocation_data or "route" not in allocation_data:
            return {
                "file": os.path.basename(file_path),
                "status": "failed",
                "error": "Not a valid allocation JSON",
            }

        # Extract filename for logging
        filename = os.path.basename(file_path)
        name = filename.replace(".json", "")

        # Run simulation using the internal function
        result = simulate_allocation_internal_api(
            allocation_data=allocation_data,
            v3_pools_data=v3_pools_data,
            v2_pools_data=v2_pools_data,
            name=name,
        )

        if "error" in result:
            return {"file": filename, "status": "failed", "error": result["error"]}

        # Update the allocation data with toAmount
        allocation_data["toAmount"] = result["toAmount"]

        # Write back to the file
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(allocation_data, f, indent=2)

        return {
            "file": filename,
            "status": "success",
            "fromAmount": allocation_data["fromAmount"],
            "toAmount": result["toAmount"],
            "from_token": allocation_data["from"],
            "to_token": allocation_data["to"],
        }

    except Exception as e:
        return {
            "file": os.path.basename(file_path),
            "status": "failed",
            "error": f"Unexpected error: {str(e)}",
        }


def simulate_allocation_internal_api(
    allocation_data: dict, v3_pools_data: list, v2_pools_data: list, name: str
) -> dict:
    """
    Internal function to simulate a single allocation using API.
    Processes fills in their original sequence (not topological sorting).
    """
    try:
        # Extract information from allocation data
        input_amount = int(allocation_data["fromAmount"])
        input_token = allocation_data["from"].lower()
        target_token = allocation_data["to"].lower()
        fills = allocation_data["route"]["fills"]

        # Create debug logger for this simulation
        debug_log_path = f"results/log/simulation_debug_{name}.txt"
        os.makedirs("results/log", exist_ok=True)

        def log_collect(msg):
            with open(debug_log_path, "a", encoding="utf-8") as debug_log:
                debug_log.write(f"{msg}\n")

        log_collect(f"Starting sequential API-based simulation for {name}")
        log_collect(f"From: {input_token}, To: {target_token}, Amount: {input_amount}")
        log_collect(f"Number of fills: {len(fills)}")

        # Track token balances
        token_balances = {}
        token_balances[input_token] = input_amount
        flow_bps = {}
        next_flow_bps = {}

        flow_bps[input_token] = 10000
        next_flow_bps[input_token] = 0
        current_token = input_token
        next_token_balance = {}

        log_collect(f"Starting sequential simulation with {len(fills)} fills")
        log_collect(f"Initial balance: {input_token} = {input_amount}")

        # Process fills in their original sequence
        for i, fill in enumerate(fills):
            token_in_addr = fill["from"].lower()
            token_out_addr = fill["to"].lower()
            pool_addr = fill["pool"].lower()
            source = fill["source"]
            proportion_bps = int(
                fill.get("proportionBps", fill.get("poolTotalProportionBps", 0))
            )

            log_collect(f"current_token: {current_token}")
            log_collect(f"token_in_addr: {token_in_addr}")
            log_collect(f"token_out_addr: {token_out_addr}")
            log_collect(f"proportion_bps: {proportion_bps}")
            log_collect(f"flow_bps: {flow_bps}")
            log_collect(f"next_flow_bps: {next_flow_bps}")
            log_collect(f"next_token_balance: {next_token_balance}")
            log_collect("--------------------------------")

            if current_token != token_in_addr:
                if token_in_addr not in flow_bps:
                    flow_bps[token_in_addr] = 0
                if token_in_addr not in token_balances:
                    token_balances[token_in_addr] = 0
                flow_bps[token_in_addr] += next_flow_bps[token_in_addr]
                next_flow_bps[token_in_addr] = 0
                current_token = token_in_addr
                if token_in_addr == input_token:
                    token_balances[token_in_addr] = input_amount
                else:
                    token_balances[token_in_addr] += next_token_balance[token_in_addr]
                    next_token_balance[token_in_addr] = 0

            # print("test1")
            # calculate the next flow bps
            if token_out_addr not in next_flow_bps:
                next_flow_bps[token_out_addr] = 0
            next_flow_bps[token_out_addr] += proportion_bps
            # print(f"next_flow_bps[token_out_addr]: {next_flow_bps[token_out_addr]}")
            # print("test2")
            log_collect(
                f"\nFill {i+1}/{len(fills)}: {token_in_addr} -> {token_out_addr}"
            )
            log_collect(f"  Pool: {pool_addr}")
            log_collect(f"  Source: {source}")
            log_collect(f"  Proportion: {proportion_bps/100:.2f}%")

            # Check if we have the input token
            if (
                token_in_addr not in token_balances
                or token_balances[token_in_addr] <= 0
            ):
                log_collect(
                    f"  Warning: No balance for input token {token_in_addr}, skipping"
                )
                continue

            # Calculate input amount based on proportion
            available_amount = token_balances[token_in_addr]
            if proportion_bps <= 0:
                log_collect(f"  Warning: Invalid proportion {proportion_bps}, skipping")
                continue

            # Use the entire available amount for this token (assuming proportion is relative to available amount)
            # allocated_in = int(available_amount * proportion_bps / 10000)
            allocated_in = int(
                float(proportion_bps / flow_bps[token_in_addr]) * available_amount
            )
            if allocated_in <= 0:
                log_collect(f"  Warning: Allocated amount is 0, skipping")
                continue

            log_collect(f"  Available: {available_amount}, Allocated: {allocated_in}")

            # Perform single hop simulation using API with update
            out_amt = single_hop_simulation_api(
                token_in_addr=token_in_addr,
                token_out_addr=token_out_addr,
                pool_addr=pool_addr,
                pool_type=source,
                amount_in=allocated_in,
                v3_pools_data=v3_pools_data,
                v2_pools_data=v2_pools_data,
                log_collect=log_collect,
                with_update=True,
            )

            log_collect(f"  Output: {out_amt}")

            # print("test")
            # Update token balances
            # token_balances[token_in_addr] -= allocated_in
            # if token_out_addr not in token_balances:
            # token_balances[token_out_addr] = 0
            # token_balances[token_out_addr] += out_amt

            if token_out_addr not in next_token_balance:
                next_token_balance[token_out_addr] = 0
            # next_token_balance[token_out_addr] = next_token_balance.get(token_out_addr, 0)
            next_token_balance[token_out_addr] += out_amt
            # print("test2")
            # print(f"next_token_balance: {next_token_balance}")

            # log_collect(f"  New balance {token_in_addr}: {token_balances[token_in_addr]}")
            # log_collect(f"  New balance {token_out_addr}: {token_balances[token_out_addr]}")

        # The final output is the balance of the target token
        final_output = next_token_balance.get(target_token, 0)
        log_collect(f"\nFinal output for {target_token}: {final_output}")

        return {"toAmount": str(final_output)}

    except Exception as e:
        return {"error": str(e)}


def simulate_and_update_folder_api(
    folder_path: str, v3_pools_file: str, v2_pools_file: str, block_number: int
) -> dict:
    """
    Simulate all JSON files in a folder using API and update them with toAmount results.
    Similar to simulate_and_update_folder in path_simulation_v3.py but uses API calls.
    """
    # Find all JSON files in the folder
    json_files = glob.glob(os.path.join(folder_path, "*.json"))

    if not json_files:
        print(f"No JSON files found in folder: {folder_path}")
        return {"error": "No JSON files found"}

    print(f"Found {len(json_files)} JSON files to process in {folder_path}")

    # Create output directories
    os.makedirs("results", exist_ok=True)
    os.makedirs("results/simulation_json", exist_ok=True)
    os.makedirs("results/log", exist_ok=True)

    # Create summary log file
    summary_log_path = f"results/log/batch_simulation_api_summary.txt"
    summary_log = open(summary_log_path, "w", encoding="utf-8")

    def log_summary(message):
        print(message)
        summary_log.write(message + "\n")

    log_summary(f"Starting API-based batch simulation for {len(json_files)} files")
    log_summary(f"Block number: {block_number}")
    log_summary(f"Folder: {folder_path}")
    log_summary(f"API URL: {API_BASE_URL}")
    log_summary("-" * 80)

    # Load pools data once for all simulations
    log_summary("Pools data will be reloaded for each file simulation")
    log_summary(f"V3 pools file: {v3_pools_file}")
    log_summary(f"V2 pools file: {v2_pools_file}")

    results_summary = {
        "total_files": len(json_files),
        "successful": 0,
        "failed": 0,
        "results": [],
    }

    # Process each JSON file using the single file function
    for i, json_file in enumerate(json_files, 1):
        filename = os.path.basename(json_file)
        log_summary(f"\n[{i}/{len(json_files)}] Processing: {filename}")

        # Use the single file simulation function
        file_result = simulate_single_allocation_file(
            file_path=json_file,
            v3_pools_file=v3_pools_file,
            v2_pools_file=v2_pools_file,
        )

        if file_result["status"] == "success":
            log_summary(
                f"  Success: Updated {filename} with toAmount = {file_result['toAmount']}"
            )
            log_summary(
                f"  From: {file_result['fromAmount']} -> To: {file_result['toAmount']}"
            )
            results_summary["successful"] += 1
        else:
            log_summary(f"  Failed: {file_result['error']}")
            results_summary["failed"] += 1

        results_summary["results"].append(file_result)
        time.sleep(1)

    # Write summary
    log_summary("\n" + "=" * 80)
    log_summary("API-BASED BATCH SIMULATION SUMMARY")
    log_summary("=" * 80)
    log_summary(f"Total files processed: {results_summary['total_files']}")
    log_summary(f"Successful simulations: {results_summary['successful']}")
    log_summary(f"Failed simulations: {results_summary['failed']}")
    log_summary(
        f"Success rate: {results_summary['successful']/results_summary['total_files']*100:.1f}%"
    )

    # Save detailed results
    results_file = f"results/simulation_json/batch_results_api_summary.json"
    with open(results_file, "w", encoding="utf-8") as f:
        json.dump(results_summary, f, indent=2)

    log_summary(f"\nDetailed results saved to: {results_file}")
    log_summary(f"Summary log saved to: {summary_log_path}")

    summary_log.close()
    return results_summary


# Test function using the allocation file
def example_test():

    import argparse

    parser = argparse.ArgumentParser(description="Test simulator with allocation files")
    parser.add_argument(
        "--mode",
        type=str,
        choices=["single", "batch"],
        default="single",
        help="Mode: single file test or batch processing",
    )
    parser.add_argument(
        "--folder",
        type=str,
        default="results/path_allocation_json",
        help="Folder for batch processing",
    )
    parser.add_argument(
        "--block", type=int, default=21974203, help="Block number for simulation"
    )
    parser.add_argument(
        "--file",
        type=str,
        default="results/path_allocation_json/allocation_RETH-WSTETH_500000000000000000000.json",
        help="file for single processing",
    )
    args = parser.parse_args()

    # Configuration
    block_number = args.block
    v3_pools_file = f"data/prices/block-{block_number}/filtered-v3pools.json"
    v2_pools_file = f"data/prices/block-{block_number}/filtered-v2pools.json"

    if args.mode == "batch":
        print(f"=== API-Based Batch Processing ===")
        print(f"Folder: {args.folder}")
        print(f"Block: {block_number}")
        print(f"API URL: {API_BASE_URL}")
        print()

        # Run batch processing
        results = simulate_and_update_folder_api(
            folder_path=args.folder,
            v3_pools_file=v3_pools_file,
            v2_pools_file=v2_pools_file,
            block_number=block_number,
        )

        if "error" in results:
            print(f"Batch processing failed: {results['error']}")
        else:
            print(f"\nBatch processing completed!")
            print(f"Total files: {results['total_files']}")
            print(f"Successful: {results['successful']}")
            print(f"Failed: {results['failed']}")
            print(
                f"Success rate: {results['successful']/results['total_files']*100:.1f}%"
            )

    else:
        # Single file test
        try:
            # Load pools data
            with open(v3_pools_file, "r", encoding="utf-8") as file:
                v3_pools_data = json.load(file)
            with open(v2_pools_file, "r", encoding="utf-8") as file:
                v2_pools_data = json.load(file)

            print("Loading pools data success")

            # Test file path
            # allocation_file = 'results/path_allocation_json/allocation_USDT-FRAX_200000000000.json'
            allocation_file = args.file

            print("=== Testing Single Allocation File Simulation ===")
            print(f"File: {allocation_file}")
            print()

            # Test the single file simulation function
            result = simulate_single_allocation_file(
                file_path=allocation_file,
                v3_pools_file=v3_pools_file,
                v2_pools_file=v2_pools_file,
            )

            if result["status"] == "success":
                print(f"Simulation successful!")
                print(f"From: {result['from_token']} -> To: {result['to_token']}")
                print(f"Amount: {result['fromAmount']} -> {result['toAmount']}")
            else:
                print(f"Simulation failed: {result['error']}")

        except Exception as e:
            print(f"Error: {e}")
            print("Make sure the API server is running on http://127.0.0.1:8080")
            print("Make sure the allocation file and pools data exist")


def main():
    """
    Test function demonstrating all four swap functions using examples from allocation file
    """
    try:
        # Load both V3 and V2 pools data
        v3_pools_file = "data/prices/block-21974203/filtered-v3pools.json"
        v2_pools_file = "data/prices/block-21974203/filtered-v2pools.json"

        with open(v3_pools_file, "r", encoding="utf-8") as file:
            v3_pools_data = json.load(file)
        with open(v2_pools_file, "r", encoding="utf-8") as file:
            v2_pools_data = json.load(file)

        print("Loading pools data success")

        # Build lookup tables
        v3_pool_data_map = {p["poolAddress"].lower(): p for p in v3_pools_data}
        v2_pool_data_map = {p["poolAddress"].lower(): p for p in v2_pools_data}

        # Use a small test amount for all examples
        test_amount = 1000000000  # 1 USDT (6 decimals)

        print("=" * 80)
        print("TESTING ALL FOUR SWAP FUNCTIONS")
        print("=" * 80)

        # Example 1: V3 swap (USDT -> USDC) - token0 to token1
        print("\n1. V3 Swap: USDT -> USDC (token0 -> token1)")
        print("-" * 50)
        v3_pool1_addr = "0x3416cf6c708da44db2624d63ea0aaef7113527c6"  # USDT-USDC pool
        v3_pool1 = v3_pool_data_map.get(v3_pool1_addr.lower())
        if v3_pool1:
            print(
                f"Pool: {v3_pool1['poolState']['poolStaticInfo']['token0Symbol']}-{v3_pool1['poolState']['poolStaticInfo']['token1Symbol']}"
            )
            print(f"Address: {v3_pool1['poolAddress']}")
            token_in = "******************************************"  # USDT
            token_out = "******************************************"  # USDC
            result = simulate_swap_v3(v3_pool1, test_amount, token_in, with_update=True)
            # print(result)
            print(f"Input: {test_amount} USDT")
            print(f"Output: {result['token0_output']} USDC")
        else:
            print("Pool not found!")

        # Example 2: V3 swap (USDC -> WETH) - token0 to token1
        print("\n2. V3 Swap: USDC -> WETH (token0 -> token1)")
        print("-" * 50)
        v3_pool2_addr = "******************************************"  # USDC-WETH pool
        v3_pool2 = v3_pool_data_map.get(v3_pool2_addr.lower())
        if v3_pool2:
            print(
                f"Pool: {v3_pool2['poolState']['poolStaticInfo']['token0Symbol']}-{v3_pool2['poolState']['poolStaticInfo']['token1Symbol']}"
            )
            print(f"Address: {v3_pool2['poolAddress']}")
            token_in = "******************************************"  # USDC
            token_out = "******************************************"  # WETH
            result = simulate_swap_v3(v3_pool2, test_amount, token_in, with_update=True)
            print(f"Input: {test_amount} USDC")
            print(f"Output: {result['token1_output']} WETH")
        else:
            print("Pool not found!")

        # Example 3: V2 swap (USDT -> WETH) - token0 to token1
        print("\n3. V2 Swap: USDT -> WETH (token0 -> token1)")
        print("-" * 50)
        v2_pool_addr = "******************************************"  # USDT-WETH V2 pool
        v2_pool = v2_pool_data_map.get(v2_pool_addr.lower())
        if v2_pool:
            print(
                f"Pool: {v2_pool['poolState']['poolStaticInfo']['token0Symbol']}-{v2_pool['poolState']['poolStaticInfo']['token1Symbol']}"
            )
            print(f"Address: {v2_pool['poolAddress']}")
            token_in = "******************************************"  # USDT
            token_out = "******************************************"  # WETH
            result = simulate_swap_v2(v2_pool, test_amount, token_in, with_update=True)
            print(f"Input: {test_amount} USDT")
            print(f"Output: {result['token0_output']} WETH")
        else:
            print("Pool not found!")

        print("\n" + "=" * 80)
        print("ALL TESTS COMPLETED")
        print("=" * 80)

        route": {
    "fills": [
      {
        "from": "******************************************",
        "to": "******************************************",
        "pool": "******************************************",
        "source": "Uniswap_V3",
        "proportionBps": "948"
      },
      {
        "from": "******************************************",
        "to": "******************************************",
        "pool": "******************************************",
        "source": "Uniswap_V3",
        "proportionBps": "948"
      }
    ]

    except Exception as e:
        print(f"Error: {e}")
        print("Make sure the API server is running on http://127.0.0.1:8080")
        print("Make sure the pools data files exist")
        e.printStackTrace()


if __name__ == "__main__":
    example_test()
    # main()
