{"blockNumber": 23423629, "fromAmount": "1000000000000", "from": "******************************************", "toAmount": "38041888163433", "to": "******************************************", "route": {"fills": [{"from": "******************************************", "to": "******************************************", "pool": "0x3416cf6c708da44db2624d63ea0aaef7113527c6", "source": "Uniswap_V3", "proportionBps": "75"}, {"from": "******************************************", "to": "******************************************", "pool": "0x7858e59e0c01ea06df3af3d20ac7b0003275d4bf", "source": "Uniswap_V3", "proportionBps": "67"}, {"from": "******************************************", "to": "******************************************", "pool": "0xdd2e0d86a45e4ef9bd490c2809e6405720cc357c", "source": "Uniswap_V3", "proportionBps": "227"}, {"from": "******************************************", "to": "******************************************", "pool": "0xc30c8b862f7de6ba5d7eaeb113c78ec6b5ded04b", "source": "Uniswap_V3", "proportionBps": "882"}, {"from": "******************************************", "to": "******************************************", "pool": "0x11b815efb8f581194ae79006d24e0d814b7697f6", "source": "Uniswap_V3", "proportionBps": "201"}, {"from": "******************************************", "to": "******************************************", "pool": "0xc7bbec68d12a0d1830360f8ec58fa599ba1b0e9b", "source": "Uniswap_V3", "proportionBps": "211"}, {"from": "******************************************", "to": "******************************************", "pool": "0x4e68ccd3e89f51c3074ca5072bbac773960dfa36", "source": "Uniswap_V3", "proportionBps": "889"}, {"from": "******************************************", "to": "******************************************", "pool": "0x9db9e0e53058c89e5b94e29621a205198648425b", "source": "Uniswap_V3", "proportionBps": "179"}, {"from": "******************************************", "to": "******************************************", "pool": "0x56534741cd8b152df6d48adf7ac51f75169a83b2", "source": "Uniswap_V3", "proportionBps": "160"}, {"from": "******************************************", "to": "******************************************", "pool": "0x9db9e0e53058c89e5b94e29621a205198648425b", "source": "Uniswap_V3", "proportionBps": "266"}, {"from": "******************************************", "to": "******************************************", "pool": "0x435664008f38b0650fbc1c9fc971d0a3bc2f1e47", "source": "Uniswap_V3", "proportionBps": "2980"}, {"from": "******************************************", "to": "******************************************", "pool": "0x6f48eca74b38d2936b02ab603ff4e36a6c0e3a77", "source": "Uniswap_V3", "proportionBps": "60"}, {"from": "******************************************", "to": "******************************************", "pool": "0x48da0965ab2d2cbf1c17c09cfb5cbe67ad5b1406", "source": "Uniswap_V3", "proportionBps": "3748"}, {"from": "******************************************", "to": "******************************************", "pool": "0x48da0965ab2d2cbf1c17c09cfb5cbe67ad5b1406", "source": "Uniswap_V3", "proportionBps": "46"}, {"from": "******************************************", "to": "******************************************", "pool": "0x0da6253560822973185297d5f32ff8fa38243afe", "source": "Uniswap_V3", "proportionBps": "50"}, {"from": "******************************************", "to": "******************************************", "pool": "0xe8f7c89c5efa061e340f2d2f206ec78fd8f7e124", "source": "Uniswap_V3", "proportionBps": "290"}, {"from": "******************************************", "to": "******************************************", "pool": "0xa478c2975ab1ea89e8196811f51a7b7ade33eb11", "source": "Uniswap_V2", "proportionBps": "184"}, {"from": "******************************************", "to": "******************************************", "pool": "0x5777d92f208679db4b9778590fa3cab3ac9e2168", "source": "Uniswap_V3", "proportionBps": "2500"}, {"from": "******************************************", "to": "******************************************", "pool": "0x6c6bc977e13df9b0de53b251522280bb72383700", "source": "Uniswap_V3", "proportionBps": "419"}, {"from": "******************************************", "to": "******************************************", "pool": "0x5777d92f208679db4b9778590fa3cab3ac9e2168", "source": "Uniswap_V3", "proportionBps": "228"}, {"from": "******************************************", "to": "******************************************", "pool": "0xe9f1e2ef814f5686c30ce6fb7103d0f780836c67", "source": "Uniswap_V3", "proportionBps": "311"}, {"from": "******************************************", "to": "******************************************", "pool": "0x97e7d56a0408570ba1a7852de36350f7713906ec", "source": "Uniswap_V3", "proportionBps": "104"}, {"from": "******************************************", "to": "******************************************", "pool": "0xa4e0faa58465a2d369aa21b3e42d43374c6f9613", "source": "Uniswap_V3", "proportionBps": "146"}, {"from": "******************************************", "to": "******************************************", "pool": "0x7a415b19932c0105c82fdb6b720bb01b0cc2cae3", "source": "Uniswap_V3", "proportionBps": "1156"}, {"from": "******************************************", "to": "******************************************", "pool": "0x13394005c1012e708fce1eb974f1130fdc73a5ce", "source": "Uniswap_V3", "proportionBps": "1110"}, {"from": "******************************************", "to": "******************************************", "pool": "0x735cdc75fb1f24f53bb8ffa4e7eb2d795005210f", "source": "Uniswap_V3", "proportionBps": "142"}, {"from": "******************************************", "to": "******************************************", "pool": "0xb4e16d0168e52d35cacd2c6185b44281ec28c9dc", "source": "Uniswap_V2", "proportionBps": "61"}, {"from": "******************************************", "to": "******************************************", "pool": "0x8ad599c3a0ff1de082011efddc58f1908eb6e6d8", "source": "Uniswap_V3", "proportionBps": "2423"}, {"from": "******************************************", "to": "******************************************", "pool": "0x88e6a0c2ddd26feeb64f039a2c41296fcb3f5640", "source": "Uniswap_V3", "proportionBps": "1545"}, {"from": "******************************************", "to": "******************************************", "pool": "0xc2e9f25be6257c210d7adf0d4cd6e3e881ba25f8", "source": "Uniswap_V3", "proportionBps": "106"}, {"from": "******************************************", "to": "******************************************", "pool": "0x8aee53b873176d9f938d24a53a8ae5cf36276464", "source": "Uniswap_V3", "proportionBps": "311"}, {"from": "******************************************", "to": "******************************************", "pool": "0x9a772018fbd77fcd2d25657e5c547baff3fd7d16", "source": "Uniswap_V3", "proportionBps": "84"}, {"from": "******************************************", "to": "******************************************", "pool": "0x4585fe77225b41b697c938b018e2ac67ac5a20c0", "source": "Uniswap_V3", "proportionBps": "181"}, {"from": "******************************************", "to": "******************************************", "pool": "0xe6d7ebb9f1a9519dc06d557e03c522d53520e76a", "source": "Uniswap_V3", "proportionBps": "2980"}, {"from": "******************************************", "to": "******************************************", "pool": "0x553e9c493678d8606d6a5ba284643db2110df823", "source": "Uniswap_V3", "proportionBps": "146"}, {"from": "******************************************", "to": "******************************************", "pool": "0xcf7e21b96a7dae8e1663b5a266fd812cbe973e70", "source": "Uniswap_V3", "proportionBps": "37"}, {"from": "******************************************", "to": "******************************************", "pool": "0xe0554a476a092703abdb3ef35c80e0d76d32939f", "source": "Uniswap_V3", "proportionBps": "146"}, {"from": "******************************************", "to": "******************************************", "pool": "0x54e58c986818903d2d86dafe03f5f5e6c2ca6710", "source": "Uniswap_V3", "proportionBps": "340"}, {"from": "******************************************", "to": "******************************************", "pool": "0xc63b0708e2f7e69cb8a1df0e1389a98c35a76d52", "source": "Uniswap_V3", "proportionBps": "104"}, {"from": "******************************************", "to": "******************************************", "pool": "0x202a6012894ae5c288ea824cbc8a9bfb26a49b93", "source": "Uniswap_V3", "proportionBps": "1156"}, {"from": "******************************************", "to": "******************************************", "pool": "0x88051b0eea095007d3bef21ab287be961f3d8598", "source": "Uniswap_V3", "proportionBps": "5622"}, {"from": "******************************************", "to": "******************************************", "pool": "0xa3cb7413d48d2d7d82e0882f2983b24cae2aa306", "source": "Uniswap_V3", "proportionBps": "37"}, {"from": "******************************************", "to": "******************************************", "pool": "******************************************", "source": "Uniswap_V3", "proportionBps": "4197"}, {"from": "******************************************", "to": "******************************************", "pool": "******************************************", "source": "Uniswap_V3", "proportionBps": "142"}], "tokens": [{"address": "******************************************", "symbol": "gOHM"}, {"address": "******************************************", "symbol": "WBTC"}, {"address": "******************************************", "symbol": "USDe"}, {"address": "******************************************", "symbol": "OHM"}, {"address": "******************************************", "symbol": "DAI"}, {"address": "******************************************", "symbol": "PYUSD"}, {"address": "******************************************", "symbol": "FRAX"}, {"address": "******************************************", "symbol": "USDC"}, {"address": "******************************************", "symbol": "sUSDS"}, {"address": "******************************************", "symbol": "rETH"}, {"address": "******************************************", "symbol": "WETH"}, {"address": "******************************************", "symbol": "cbBTC"}, {"address": "******************************************", "symbol": "weETH"}, {"address": "******************************************", "symbol": "USDT"}, {"address": "******************************************", "symbol": "USDS"}]}}