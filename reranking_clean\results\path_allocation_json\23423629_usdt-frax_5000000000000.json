{"blockNumber": 23423629, "fromAmount": "5000000000000", "from": "******************************************", "toAmount": "1338166594092759493844161", "to": "******************************************", "route": {"fills": [{"from": "******************************************", "to": "******************************************", "pool": "0x0d4a11d5eeaac28ec3f61d100daf4d40471f1852", "source": "Uniswap_V2", "proportionBps": "167"}, {"from": "******************************************", "to": "******************************************", "pool": "0x11b815efb8f581194ae79006d24e0d814b7697f6", "source": "Uniswap_V3", "proportionBps": "246"}, {"from": "******************************************", "to": "******************************************", "pool": "0xc7bbec68d12a0d1830360f8ec58fa599ba1b0e9b", "source": "Uniswap_V3", "proportionBps": "249"}, {"from": "******************************************", "to": "******************************************", "pool": "0x4e68ccd3e89f51c3074ca5072bbac773960dfa36", "source": "Uniswap_V3", "proportionBps": "1826"}, {"from": "******************************************", "to": "******************************************", "pool": "0x0d4a11d5eeaac28ec3f61d100daf4d40471f1852", "source": "Uniswap_V2", "proportionBps": "34"}, {"from": "******************************************", "to": "******************************************", "pool": "0x3041cbd36888becc7bbcbc0045e3b1f144466f5f", "source": "Uniswap_V2", "proportionBps": "3"}, {"from": "******************************************", "to": "******************************************", "pool": "0x3416cf6c708da44db2624d63ea0aaef7113527c6", "source": "Uniswap_V3", "proportionBps": "1804"}, {"from": "******************************************", "to": "******************************************", "pool": "0xee4cf3b78a74affa38c6a926282bcd8b5952818d", "source": "Uniswap_V3", "proportionBps": "46"}, {"from": "******************************************", "to": "******************************************", "pool": "0x7858e59e0c01ea06df3af3d20ac7b0003275d4bf", "source": "Uniswap_V3", "proportionBps": "910"}, {"from": "******************************************", "to": "******************************************", "pool": "0xdd2e0d86a45e4ef9bd490c2809e6405720cc357c", "source": "Uniswap_V3", "proportionBps": "131"}, {"from": "******************************************", "to": "******************************************", "pool": "0xc30c8b862f7de6ba5d7eaeb113c78ec6b5ded04b", "source": "Uniswap_V3", "proportionBps": "142"}, {"from": "******************************************", "to": "******************************************", "pool": "0x39f9ff86479579952e7218c27ab9d2a9ff9bfe3e", "source": "Uniswap_V3", "proportionBps": "3"}, {"from": "******************************************", "to": "******************************************", "pool": "0x9db9e0e53058c89e5b94e29621a205198648425b", "source": "Uniswap_V3", "proportionBps": "2082"}, {"from": "******************************************", "to": "******************************************", "pool": "0x56534741cd8b152df6d48adf7ac51f75169a83b2", "source": "Uniswap_V3", "proportionBps": "328"}, {"from": "******************************************", "to": "******************************************", "pool": "0x435664008f38b0650fbc1c9fc971d0a3bc2f1e47", "source": "Uniswap_V3", "proportionBps": "360"}, {"from": "******************************************", "to": "******************************************", "pool": "0xde77450d0887994364b62a93e14a3a76d2db9162", "source": "Uniswap_V3", "proportionBps": "3"}, {"from": "******************************************", "to": "******************************************", "pool": "0x6f48eca74b38d2936b02ab603ff4e36a6c0e3a77", "source": "Uniswap_V3", "proportionBps": "58"}, {"from": "******************************************", "to": "******************************************", "pool": "0x48da0965ab2d2cbf1c17c09cfb5cbe67ad5b1406", "source": "Uniswap_V3", "proportionBps": "1591"}, {"from": "******************************************", "to": "******************************************", "pool": "0x3470447f3cecffac709d3e783a307790b0208d60", "source": "Uniswap_V3", "proportionBps": "5"}, {"from": "******************************************", "to": "******************************************", "pool": "0xf7878463070a013a58f547b2b08df47a1fb91744", "source": "Uniswap_V3", "proportionBps": "2"}, {"from": "******************************************", "to": "******************************************", "pool": "0xa478c2975ab1ea89e8196811f51a7b7ade33eb11", "source": "Uniswap_V2", "proportionBps": "21"}, {"from": "******************************************", "to": "******************************************", "pool": "0xc2e9f25be6257c210d7adf0d4cd6e3e881ba25f8", "source": "Uniswap_V3", "proportionBps": "21"}, {"from": "******************************************", "to": "******************************************", "pool": "0xa80964c5bbd1a0e95777094420555fead1a26c1e", "source": "Uniswap_V3", "proportionBps": "5"}, {"from": "******************************************", "to": "******************************************", "pool": "0x60594a405d53811d3bc4766596efd80fd545a270", "source": "Uniswap_V3", "proportionBps": "21"}, {"from": "******************************************", "to": "******************************************", "pool": "0xb4e16d0168e52d35cacd2c6185b44281ec28c9dc", "source": "Uniswap_V2", "proportionBps": "162"}, {"from": "******************************************", "to": "******************************************", "pool": "0x7bea39867e4169dbe237d55c8242a8f2fcdcc387", "source": "Uniswap_V3", "proportionBps": "15"}, {"from": "******************************************", "to": "******************************************", "pool": "0x8ad599c3a0ff1de082011efddc58f1908eb6e6d8", "source": "Uniswap_V3", "proportionBps": "526"}, {"from": "******************************************", "to": "******************************************", "pool": "0xe0554a476a092703abdb3ef35c80e0d76d32939f", "source": "Uniswap_V3", "proportionBps": "345"}, {"from": "******************************************", "to": "******************************************", "pool": "0x88e6a0c2ddd26feeb64f039a2c41296fcb3f5640", "source": "Uniswap_V3", "proportionBps": "589"}, {"from": "******************************************", "to": "******************************************", "pool": "0xecba967d84fcf0405f6b32bc45f4d36bfdbb2e81", "source": "Uniswap_V2", "proportionBps": "47"}, {"from": "******************************************", "to": "******************************************", "pool": "0x39c9e3128b8736e02a30b2b9b7e50ff522b935c5", "source": "Uniswap_V3", "proportionBps": "16"}, {"from": "******************************************", "to": "******************************************", "pool": "0xcd8286b48936cdac20518247dbd310ab681a9fbf", "source": "Uniswap_V3", "proportionBps": "715"}, {"from": "******************************************", "to": "******************************************", "pool": "0x8a15b2dc9c4f295dcebb0e7887dd25980088fdcb", "source": "Uniswap_V3", "proportionBps": "2"}, {"from": "******************************************", "to": "******************************************", "pool": "0x231b7589426ffe1b75405526fc32ac09d44364c4", "source": "Uniswap_V2", "proportionBps": "2"}, {"from": "******************************************", "to": "******************************************", "pool": "0x391e8501b626c623d39474afca6f9e46c2686649", "source": "Uniswap_V3", "proportionBps": "7"}, {"from": "******************************************", "to": "******************************************", "pool": "0xbb2b8038a1640196fbe3e38816f3e67cba72d940", "source": "Uniswap_V2", "proportionBps": "20"}, {"from": "******************************************", "to": "******************************************", "pool": "0x6ab3bba2f41e7eaa262fa5a1a9b3932fa161526f", "source": "Uniswap_V3", "proportionBps": "9"}, {"from": "******************************************", "to": "******************************************", "pool": "0x4585fe77225b41b697c938b018e2ac67ac5a20c0", "source": "Uniswap_V3", "proportionBps": "23"}, {"from": "******************************************", "to": "******************************************", "pool": "0xcbcdf9626bc03e24f779434178a73a0b4bad62ed", "source": "Uniswap_V3", "proportionBps": "21"}, {"from": "******************************************", "to": "******************************************", "pool": "0x9a772018fbd77fcd2d25657e5c547baff3fd7d16", "source": "Uniswap_V3", "proportionBps": "140"}, {"from": "******************************************", "to": "******************************************", "pool": "0x99ac8ca7087fa4a2a1fb6357269965a2014abc35", "source": "Uniswap_V3", "proportionBps": "2184"}, {"from": "******************************************", "to": "******************************************", "pool": "0x3e8468f66d30fc99f745481d4b383f89861702c6", "source": "Uniswap_V2", "proportionBps": "2"}, {"from": "******************************************", "to": "******************************************", "pool": "0x24ee2c6b9597f035088cda8575e9d5e15a84b9df", "source": "Uniswap_V3", "proportionBps": "3"}, {"from": "******************************************", "to": "******************************************", "pool": "0xe6d7ebb9f1a9519dc06d557e03c522d53520e76a", "source": "Uniswap_V3", "proportionBps": "360"}, {"from": "******************************************", "to": "******************************************", "pool": "0x13394005c1012e708fce1eb974f1130fdc73a5ce", "source": "Uniswap_V3", "proportionBps": "274"}, {"from": "******************************************", "to": "******************************************", "pool": "0x7b1e5d984a43ee732de195628d20d05cfabc3cc7", "source": "Uniswap_V3", "proportionBps": "3"}, {"from": "******************************************", "to": "******************************************", "pool": "0xd0fc8ba7e267f2bc56044a7715a489d851dc6d78", "source": "Uniswap_V3", "proportionBps": "5"}, {"from": "******************************************", "to": "******************************************", "pool": "0x5777d92f208679db4b9778590fa3cab3ac9e2168", "source": "Uniswap_V3", "proportionBps": "975"}, {"from": "******************************************", "to": "******************************************", "pool": "0x6c6bc977e13df9b0de53b251522280bb72383700", "source": "Uniswap_V3", "proportionBps": "674"}, {"from": "******************************************", "to": "******************************************", "pool": "0xe1573b9d29e2183b1af0e743dc2754979a40d237", "source": "Uniswap_V2", "proportionBps": "755"}, {"from": "******************************************", "to": "******************************************", "pool": "0xb64508b9f7b81407549e13db970dd5bb5c19107f", "source": "Uniswap_V3", "proportionBps": "24"}, {"from": "******************************************", "to": "******************************************", "pool": "0x97c4adc5d28a86f9470c70dd91dc6cc2f20d2d4d", "source": "Uniswap_V2", "proportionBps": "931"}, {"from": "******************************************", "to": "******************************************", "pool": "0x10581399a549dbfffdbd9b070a0ba2f9f61620d2", "source": "Uniswap_V3", "proportionBps": "3428"}, {"from": "******************************************", "to": "******************************************", "pool": "0x9a834b70c07c81a9fcd6f22e842bf002fbffbe4d", "source": "Uniswap_V3", "proportionBps": "66"}, {"from": "******************************************", "to": "******************************************", "pool": "0xc63b0708e2f7e69cb8a1df0e1389a98c35a76d52", "source": "Uniswap_V3", "proportionBps": "4592"}, {"from": "******************************************", "to": "******************************************", "pool": "0xfd0a40bc83c5fae4203dec7e5929b446b07d1c76", "source": "Uniswap_V2", "proportionBps": "120"}, {"from": "******************************************", "to": "******************************************", "pool": "******************************************", "source": "Uniswap_V3", "proportionBps": "2"}, {"from": "******************************************", "to": "******************************************", "pool": "******************************************", "source": "Uniswap_V3", "proportionBps": "78"}], "tokens": [{"address": "******************************************", "symbol": "UNI"}, {"address": "******************************************", "symbol": "WBTC"}, {"address": "******************************************", "symbol": "FXS"}, {"address": "******************************************", "symbol": "QNT"}, {"address": "******************************************", "symbol": "USDe"}, {"address": "******************************************", "symbol": "frxETH"}, {"address": "******************************************", "symbol": "GNO"}, {"address": "******************************************", "symbol": "DAI"}, {"address": "******************************************", "symbol": "PYUSD"}, {"address": "******************************************", "symbol": "FRAX"}, {"address": "******************************************", "symbol": "USDC"}, {"address": "******************************************", "symbol": "WETH"}, {"address": "******************************************", "symbol": "USDT"}, {"address": "******************************************", "symbol": "ONDO"}]}}